<template>
  <div class="admin-card">
    <div class="admin-card-header">
      <h3 class="admin-card-title">
        <i class="fas fa-credit-card admin-card-icon"></i>
        Financial Information
      </h3>
    </div>
    <div class="admin-card-content">
      
      <!-- Banking Information -->
      <div class="finance-section">
        <h4 class="finance-section-title">
          <i class="fas fa-university"></i>
          Banking Details
        </h4>
        
        <div class="admin-info-grid">
          <div class="admin-info-group">
            <label class="admin-info-label">Bank Name</label>
            <div class="admin-info-value">
              {{ finance.bankName || 'N/A' }}
            </div>
          </div>
          
          <div class="admin-info-group">
            <label class="admin-info-label">Bank Code</label>
            <div class="admin-info-value admin-info-value-code">
              {{ finance.bankCode || 'N/A' }}
            </div>
          </div>
          
          <div class="admin-info-group admin-info-group-full">
            <label class="admin-info-label">Bank Account</label>
            <div class="admin-info-value admin-info-value-account">
              <span v-if="finance.bankAccount" class="account-number">
                {{ formatBankAccount(finance.bankAccount) }}
              </span>
              <span v-else class="admin-text-muted">N/A</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Tax Information -->
      <div class="finance-section">
        <h4 class="finance-section-title">
          <i class="fas fa-receipt"></i>
          Tax Information
        </h4>
        
        <div class="admin-info-grid">
          <div class="admin-info-group">
            <label class="admin-info-label">Tax ID</label>
            <div class="admin-info-value admin-info-value-code">
              {{ finance.taxId || 'N/A' }}
            </div>
          </div>
        </div>
      </div>

      <!-- Payment Details -->
      <div v-if="finance.paymentDetails" class="finance-section">
        <h4 class="finance-section-title">
          <i class="fas fa-money-check-alt"></i>
          Payment Details
        </h4>
        
        <div class="admin-info-group">
          <div class="admin-info-value admin-info-value-multiline">
            {{ finance.paymentDetails }}
          </div>
        </div>
      </div>

      <!-- Timestamps -->
      <div class="finance-timestamps">
        <div class="timestamp-item">
          <label class="timestamp-label">Created</label>
          <span class="timestamp-value">{{ formatDateTime(finance.createdAt) }}</span>
        </div>
        
        <div v-if="finance.updatedAt" class="timestamp-item">
          <label class="timestamp-label">Last Updated</label>
          <span class="timestamp-value">{{ formatDateTime(finance.updatedAt) }}</span>
        </div>
      </div>

      <!-- Security Notice -->
      <div class="security-notice">
        <i class="fas fa-shield-alt"></i>
        <span>Financial information is encrypted and securely stored</span>
      </div>

    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  finance: {
    type: Object,
    required: true,
    default: () => ({})
  }
});

const formatBankAccount = (account) => {
  if (!account) return 'N/A';
  
  // Format bank account number with spaces for better readability
  // Example: **************** -> 1234 5678 9012 3456
  const cleaned = account.replace(/\s/g, '');
  const formatted = cleaned.replace(/(.{4})/g, '$1 ').trim();
  
  return formatted;
};

const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';
  
  const date = new Date(dateString);
  return date.toLocaleString('uk-UA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};
</script>

<style scoped>
.finance-section {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--admin-border-light);
}

.finance-section:last-of-type {
  border-bottom: none;
  margin-bottom: 1rem;
}

.finance-section-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--admin-text-secondary);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.finance-section-title i {
  color: var(--admin-text-muted);
  width: 16px;
}

.admin-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.admin-info-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.admin-info-group-full {
  grid-column: 1 / -1;
}

.admin-info-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--admin-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-info-value {
  font-size: 0.9rem;
  color: var(--admin-text-primary);
  line-height: 1.5;
}

.admin-info-value-code {
  font-family: 'Courier New', monospace;
  background: var(--admin-bg-tertiary);
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.85rem;
  border: 1px solid var(--admin-border-light);
}

.admin-info-value-account {
  font-family: 'Courier New', monospace;
  background: var(--admin-bg-secondary);
  padding: 0.75rem;
  border-radius: 6px;
  border: 1px solid var(--admin-border-light);
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 0.1em;
}

.account-number {
  color: var(--admin-text-primary);
}

.admin-info-value-multiline {
  white-space: pre-wrap;
  word-break: break-word;
  background: var(--admin-bg-tertiary);
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid var(--admin-border-light);
}

.finance-timestamps {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--admin-border-light);
}

.timestamp-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.timestamp-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--admin-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.timestamp-value {
  font-size: 0.85rem;
  color: var(--admin-text-secondary);
}

.security-notice {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--admin-bg-secondary);
  border: 1px solid var(--admin-border-light);
  border-radius: 6px;
  font-size: 0.85rem;
  color: var(--admin-text-muted);
  margin-top: 1rem;
}

.security-notice i {
  color: var(--admin-success);
}

.admin-text-muted {
  color: var(--admin-text-muted);
  font-style: italic;
}

@media (max-width: 768px) {
  .admin-info-grid {
    grid-template-columns: 1fr;
  }
  
  .finance-timestamps {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
