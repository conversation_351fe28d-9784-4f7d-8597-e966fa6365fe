import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { createRouter, createWebHistory } from 'vue-router';
import CompanyDetail from '@/admin/views/companies/CompanyDetail.vue';
import { companiesService } from '@/admin/services/companies';

// Mock the companies service
vi.mock('@/admin/services/companies', () => ({
  companiesService: {
    getDetailedCompany: vi.fn(),
    approveCompany: vi.fn(),
    rejectCompany: vi.fn()
  }
}));

// Mock the components
vi.mock('@/admin/components/companies/CompanyInfoCard.vue', () => ({
  default: {
    name: 'CompanyInfoCard',
    props: ['company'],
    template: '<div data-testid="company-info-card">Company Info Card</div>'
  }
}));

vi.mock('@/admin/components/companies/CompanyFinanceCard.vue', () => ({
  default: {
    name: 'CompanyFinanceCard',
    props: ['finance'],
    template: '<div data-testid="company-finance-card">Company Finance Card</div>'
  }
}));

vi.mock('@/admin/components/companies/CompanyScheduleTable.vue', () => ({
  default: {
    name: 'CompanyScheduleTable',
    props: ['schedule'],
    template: '<div data-testid="company-schedule-table">Company Schedule Table</div>'
  }
}));

vi.mock('@/admin/components/companies/CompanyUsersTable.vue', () => ({
  default: {
    name: 'CompanyUsersTable',
    props: ['companyId'],
    template: '<div data-testid="company-users-table">Company Users Table</div>'
  }
}));

vi.mock('@/admin/components/companies/CompanyProductsTable.vue', () => ({
  default: {
    name: 'CompanyProductsTable',
    props: ['companyId'],
    template: '<div data-testid="company-products-table">Company Products Table</div>'
  }
}));

describe('CompanyDetail', () => {
  let wrapper;
  let router;

  const mockCompany = {
    id: '123',
    name: 'Test Company',
    slug: 'test-company',
    description: 'Test Description',
    contactEmail: '<EMAIL>',
    contactPhone: '+************',
    isApproved: true,
    isFeatured: false,
    finance: {
      bankName: 'Test Bank',
      bankAccount: '**********',
      bankCode: '123456',
      taxId: '**********'
    },
    schedule: [
      {
        day: 1,
        openTime: '09:00:00',
        closeTime: '18:00:00',
        isClosed: false
      }
    ]
  };

  beforeEach(() => {
    // Create router
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/admin/companies/:id', component: CompanyDetail }
      ]
    });

    // Push to the route
    router.push('/admin/companies/123');

    // Mock the service response
    companiesService.getDetailedCompany.mockResolvedValue({
      data: mockCompany
    });
  });

  it('renders loading state initially', async () => {
    wrapper = mount(CompanyDetail, {
      global: {
        plugins: [router]
      }
    });

    expect(wrapper.find('.admin-loading-state').exists()).toBe(true);
  });

  it('renders company details after loading', async () => {
    wrapper = mount(CompanyDetail, {
      global: {
        plugins: [router]
      }
    });

    // Wait for the component to load
    await wrapper.vm.$nextTick();
    await new Promise(resolve => setTimeout(resolve, 100));

    expect(wrapper.find('[data-testid="company-info-card"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="company-finance-card"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="company-schedule-table"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="company-users-table"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="company-products-table"]').exists()).toBe(true);
  });

  it('displays page title correctly', async () => {
    wrapper = mount(CompanyDetail, {
      global: {
        plugins: [router]
      }
    });

    await wrapper.vm.$nextTick();
    await new Promise(resolve => setTimeout(resolve, 100));

    expect(wrapper.find('.admin-page-title').text()).toContain('Test Company');
  });

  it('shows action buttons for unapproved companies', async () => {
    const unapprovedCompany = { ...mockCompany, isApproved: false };
    companiesService.getDetailedCompany.mockResolvedValue({
      data: unapprovedCompany
    });

    wrapper = mount(CompanyDetail, {
      global: {
        plugins: [router]
      }
    });

    await wrapper.vm.$nextTick();
    await new Promise(resolve => setTimeout(resolve, 100));

    const approveButton = wrapper.find('button:contains("Approve Company")');
    const rejectButton = wrapper.find('button:contains("Reject Company")');
    
    expect(approveButton.exists()).toBe(true);
    expect(rejectButton.exists()).toBe(true);
  });

  it('handles error state correctly', async () => {
    companiesService.getDetailedCompany.mockRejectedValue(new Error('Failed to load'));

    wrapper = mount(CompanyDetail, {
      global: {
        plugins: [router]
      }
    });

    await wrapper.vm.$nextTick();
    await new Promise(resolve => setTimeout(resolve, 100));

    expect(wrapper.find('.admin-alert-danger').exists()).toBe(true);
  });
});
