<template>
  <div class="chat-list">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Chat Management</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <div class="buttons">
            <button
              class="button is-warning"
              @click="moderateSelected"
              :disabled="selectedChats.length === 0 || actionLoading"
              v-if="selectedChats.length > 0"
            >
              <span class="icon">
                <i class="fas fa-gavel"></i>
              </span>
              <span>Moderate Selected ({{ selectedChats.length }})</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <SearchAndFilters
      v-model:search="searchQuery"
      :filters="filters"
      :filter-fields="filterFields"
      search-label="Search Chats"
      search-placeholder="Search by participants or chat content..."
      search-column-class="is-4"
      :total-items="totalItems"
      item-name="chats"
      :loading="loading"
      @search-changed="handleSearchChange"
      @filter-changed="handleFilterChange"
      @reset-filters="handleResetFilters"
    />

    <!-- Loading -->
    <div class="has-text-centered py-6" v-if="loading && isFirstLoad">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-2x"></i>
      </span>
      <p class="mt-2">Loading chats...</p>
    </div>

    <!-- Error -->
    <div class="notification is-danger" v-else-if="error">
      <p>{{ error }}</p>
      <button class="button is-light mt-2" @click="fetchChats">
        <span class="icon"><i class="fas fa-redo"></i></span>
        <span>Retry</span>
      </button>
    </div>

    <!-- Chats Table -->
    <div class="card" v-else>
      <div class="card-content">
        <div class="table-container" :class="{ 'is-loading': loading && !isFirstLoad }">
          <table class="table is-fullwidth is-hoverable">
            <thead>
              <tr>
                <th>
                  <label class="checkbox">
                    <input
                      type="checkbox"
                      @change="toggleSelectAll"
                      :checked="allSelected"
                      :indeterminate="someSelected"
                    />
                  </label>
                </th>
                <th>Participants</th>
                <th>Last Message</th>
                <th>Messages Count</th>
                <th>Status</th>
                <th>Created</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="chat in chats" :key="chat.id">
                <td>
                  <label class="checkbox">
                    <input
                      type="checkbox"
                      :value="chat.id"
                      v-model="selectedChats"
                    />
                  </label>
                </td>
                <td>
                  <div class="participants">
                    <div><strong>Buyer:</strong> {{ chat.buyerName || 'Unknown' }}</div>
                    <div><strong>Seller:</strong> {{ chat.sellerName || 'Unknown' }}</div>
                  </div>
                </td>
                <td>
                  <div class="last-message">
                    <div class="message-preview">{{ truncateText(chat.lastMessage, 50) }}</div>
                    <div class="message-time">{{ formatDate(chat.lastMessageAt) }}</div>
                  </div>
                </td>
                <td>
                  <span class="tag is-info">{{ chat.messageCount || 0 }}</span>
                </td>
                <td>
                  <span class="tag" :class="getStatusClass(chat.status)">
                    {{ formatStatus(chat.status) }}
                  </span>
                </td>
                <td>{{ formatDate(chat.createdAt) }}</td>
                <td>
                  <div class="buttons">
                    <router-link
                      :to="{ name: 'AdminChatDetail', params: { id: chat.id } }"
                      class="button is-small is-info">
                      <span class="icon">
                        <i class="fas fa-eye"></i>
                      </span>
                      <span>View</span>
                    </router-link>
                    <button
                      class="button is-small is-warning"
                      @click="moderateChat(chat.id)"
                      :disabled="actionLoading">
                      <span class="icon">
                        <i class="fas fa-gavel"></i>
                      </span>
                      <span>Moderate</span>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <Pagination
      v-if="totalPages > 1"
      :current-page="currentPage"
      :total-pages="totalPages"
      @page-changed="handlePageChange"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { chatsService } from '@/admin/services/chats';
import SearchAndFilters from '@/components/admin/SearchAndFilters.vue';
import Pagination from '@/components/admin/Pagination.vue';

// Utility function for debouncing
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Reactive data
const chats = ref([]);
const loading = ref(false);
const error = ref(null);
const actionLoading = ref(false);
const searchQuery = ref('');
const selectedChats = ref([]);
const isFirstLoad = ref(true);

// Filters
const filters = ref({
  status: '',
  hasMessages: ''
});

const filterFields = ref({
  status: {
    type: 'select',
    label: 'Status',
    options: [
      { value: 'active', label: 'Active' },
      { value: 'inactive', label: 'Inactive' },
      { value: 'moderated', label: 'Moderated' },
      { value: 'blocked', label: 'Blocked' }
    ]
  },
  hasMessages: {
    type: 'select',
    label: 'Messages',
    options: [
      { value: 'true', label: 'Has Messages' },
      { value: 'false', label: 'No Messages' }
    ]
  }
});

const sortBy = ref('createdAt');
const sortOrder = ref('desc');

// Pagination
const currentPage = ref(1);
const totalPages = ref(1);
const totalItems = ref(0);
const pageSize = ref(15);

// Computed
const allSelected = computed(() => {
  return chats.value.length > 0 && selectedChats.value.length === chats.value.length;
});

const someSelected = computed(() => {
  return selectedChats.value.length > 0 && selectedChats.value.length < chats.value.length;
});

// Methods
const fetchChats = async () => {
  loading.value = true;
  error.value = null;

  try {
    const params = {
      filter: searchQuery.value,
      status: filters.value.status,
      hasMessages: filters.value.hasMessages,
      orderBy: sortBy.value,
      descending: sortOrder.value === 'desc',
      page: currentPage.value,
      pageSize: pageSize.value
    };

    const response = await chatsService.getChats(params);
    chats.value = response.data || [];
    currentPage.value = response.currentPage || 1;
    totalPages.value = response.totalPages || 1;
    totalItems.value = response.totalItems || 0;

    // Clear selections when data changes
    selectedChats.value = [];
  } catch (err) {
    error.value = err.message || 'Failed to load chats';
    chats.value = [];
  } finally {
    loading.value = false;
    isFirstLoad.value = false;
  }
};

const handleSearchChange = (query) => {
  searchQuery.value = query;
  currentPage.value = 1;
  debouncedSearch();
};

const handleFilterChange = () => {
  currentPage.value = 1;
  fetchChats();
};

const handleResetFilters = () => {
  searchQuery.value = '';
  filters.value.status = '';
  filters.value.hasMessages = '';
  currentPage.value = 1;
  fetchChats();
};

const handlePageChange = (page) => {
  currentPage.value = page;
  fetchChats();
};

const debouncedSearch = debounce(fetchChats, 300);

const toggleSelectAll = () => {
  if (allSelected.value) {
    selectedChats.value = [];
  } else {
    selectedChats.value = chats.value.map(chat => chat.id);
  }
};

const moderateChat = async (chatId) => {
  if (!confirm('Are you sure you want to moderate this chat?')) {
    return;
  }

  actionLoading.value = true;
  try {
    await chatsService.moderateChat(chatId, 'moderate');
    await fetchChats(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to moderate chat';
  } finally {
    actionLoading.value = false;
  }
};

const moderateSelected = async () => {
  if (!confirm(`Are you sure you want to moderate ${selectedChats.value.length} selected chats?`)) {
    return;
  }

  actionLoading.value = true;
  try {
    for (const chatId of selectedChats.value) {
      await chatsService.moderateChat(chatId, 'moderate');
    }
    await fetchChats(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to moderate chats';
  } finally {
    actionLoading.value = false;
  }
};

// Utility methods
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString();
};

const truncateText = (text, length) => {
  if (!text) return 'No messages';
  return text.length > length ? text.substring(0, length) + '...' : text;
};

const formatStatus = (status) => {
  return status?.charAt(0).toUpperCase() + status?.slice(1) || 'Unknown';
};

const getStatusClass = (status) => {
  switch (status?.toLowerCase()) {
    case 'active': return 'is-success';
    case 'inactive': return 'is-light';
    case 'moderated': return 'is-warning';
    case 'blocked': return 'is-danger';
    default: return 'is-light';
  }
};

// Lifecycle
onMounted(() => {
  fetchChats();
});
</script>

<style scoped>
.chat-list {
  padding: 1rem;
}

.title {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

.table-container {
  overflow-x: auto;
}

.table {
  background-color: var(--card-bg);
  color: var(--text-primary);
}

.table th {
  background-color: var(--darker-bg);
  color: var(--text-primary);
  border-color: var(--border-color);
}

.table td {
  border-color: var(--border-color);
  color: var(--text-primary);
}

.table tbody tr:hover {
  background-color: var(--darker-bg);
}

.buttons {
  display: flex;
  gap: 0.5rem;
}

.participants {
  font-size: 0.875rem;
}

.participants div {
  margin-bottom: 0.25rem;
}

.last-message {
  max-width: 200px;
}

.message-preview {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.message-time {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.checkbox input[type="checkbox"] {
  background-color: var(--card-bg);
  border-color: var(--border-color);
}

.tag {
  font-size: 0.75rem;
}

.tag.is-success {
  background-color: var(--success-color);
  color: white;
}

.tag.is-warning {
  background-color: var(--warning-color);
  color: white;
}

.tag.is-danger {
  background-color: var(--danger-color);
  color: white;
}

.tag.is-info {
  background-color: var(--info-color);
  color: white;
}

.tag.is-light {
  background-color: var(--text-secondary);
  color: white;
}

.button.is-warning {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

.button.is-warning:hover {
  background-color: var(--warning-color-dark);
}

.button.is-info {
  background-color: var(--info-color);
  border-color: var(--info-color);
}

.button.is-info:hover {
  background-color: var(--info-color-dark);
}

.level {
  margin-bottom: 1.5rem;
}

.card {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
}

.notification.is-danger {
  background-color: var(--danger-color);
  color: white;
}
</style>
