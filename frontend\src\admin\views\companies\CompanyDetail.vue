<template>
  <div class="admin-company-detail">
    <div class="admin-page-header">
      <div class="admin-header-content">
        <div class="admin-header-left">
          <nav class="admin-breadcrumb">
            <router-link to="/admin/companies" class="admin-breadcrumb-item">
              <i class="fas fa-building"></i>
              Companies
            </router-link>
            <span class="admin-breadcrumb-separator">/</span>
            <span class="admin-breadcrumb-item admin-breadcrumb-current">
              {{ company.name || 'Company Details' }}
            </span>
          </nav>
          <h1 class="admin-page-title">
            <i class="fas fa-building"></i>
            {{ company.name || 'Company Details' }}
          </h1>
        </div>
        <div class="admin-header-right">
          <button class="admin-btn admin-btn-secondary" @click="fetchCompany" :disabled="loading">
            <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
            <span>Refresh</span>
          </button>
          <router-link
            v-if="company.id"
            :to="`/admin/companies/${company.id}/edit`"
            class="admin-btn admin-btn-primary">
            <i class="fas fa-edit"></i>
            <span>Edit Company</span>
          </router-link>
        </div>
      </div>
    </div>

    <!-- Loading -->
    <div class="admin-loading-state" v-if="loading && !company.id">
      <div class="admin-spinner">
        <i class="fas fa-spinner fa-pulse"></i>
      </div>
      <p class="admin-loading-text">Loading company details...</p>
    </div>

    <!-- Error -->
    <div class="admin-alert admin-alert-danger" v-else-if="error">
      <div class="admin-alert-icon">
        <i class="fas fa-exclamation-circle"></i>
      </div>
      <div class="admin-alert-content">
        <div class="admin-alert-message">{{ error }}</div>
      </div>
      <button class="admin-alert-close" @click="error = null">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Company Details -->
    <div v-else-if="company.id" class="admin-company-content">
      <div class="admin-grid admin-grid-company">
        <!-- Main Info -->
        <div class="admin-card admin-card-main">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-building admin-card-icon"></i>
              Company Information
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="admin-form-grid admin-form-grid-2">
              <div class="admin-info-group">
                <label class="admin-info-label">Company Name</label>
                <div class="admin-info-value">{{ company.name || 'N/A' }}</div>
              </div>
              <div class="admin-info-group">
                <label class="admin-info-label">Slug</label>
                <div class="admin-info-value admin-info-value-code">{{ company.slug || 'N/A' }}</div>
              </div>
              <div class="admin-info-group">
                <label class="admin-info-label">Contact Email</label>
                <div class="admin-info-value">
                  <a v-if="company.contactEmail" :href="`mailto:${company.contactEmail}`" class="admin-link">
                    {{ company.contactEmail }}
                  </a>
                  <span v-else>N/A</span>
                </div>
              </div>
              <div class="admin-info-group">
                <label class="admin-info-label">Contact Phone</label>
                <div class="admin-info-value">
                  <a v-if="company.contactPhone" :href="`tel:${company.contactPhone}`" class="admin-link">
                    {{ company.contactPhone }}
                  </a>
                  <span v-else>N/A</span>
                </div>
              </div>
              <div class="admin-info-group admin-info-group-full">
                <label class="admin-info-label">Description</label>
                <div class="admin-info-value admin-info-value-multiline">
                  {{ company.description || 'No description provided' }}
                </div>
              </div>
              <div class="admin-info-group admin-info-group-full">
                <label class="admin-info-label">Address</label>
                <div class="admin-info-value admin-info-value-multiline">
                  <div v-if="company.addressStreet || company.addressCity">
                    <div v-if="company.addressStreet">{{ company.addressStreet }}</div>
                    <div v-if="company.addressCity || company.addressRegion || company.addressPostalCode">
                      {{ [company.addressCity, company.addressRegion, company.addressPostalCode].filter(Boolean).join(', ') }}
                    </div>
                  </div>
                  <span v-else>No address provided</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Status Card -->
        <div class="admin-card admin-card-status">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-info-circle admin-card-icon"></i>
              Status & Approval
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="admin-status-section">
              <div class="admin-info-group">
                <label class="admin-info-label">Approval Status</label>
                <div class="admin-info-value">
                  <span class="admin-badge" :class="company.isApproved ? 'admin-badge-success' : 'admin-badge-warning'">
                    {{ company.isApproved ? 'Approved' : 'Pending' }}
                  </span>
                </div>
              </div>
              <div class="admin-info-group" v-if="company.isFeatured">
                <label class="admin-info-label">Featured</label>
                <div class="admin-info-value">
                  <span class="admin-badge admin-badge-info">Featured Company</span>
                </div>
              </div>
              <div class="admin-info-group" v-if="company.approvedAt">
                <label class="admin-info-label">Approved At</label>
                <div class="admin-info-value">{{ formatDateTime(company.approvedAt) }}</div>
              </div>
              <div class="admin-info-group" v-if="company.approvedByUserId">
                <label class="admin-info-label">Approved By</label>
                <div class="admin-info-value">User ID: {{ company.approvedByUserId }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Finance Information -->
        <div class="admin-card admin-card-finance" v-if="company.finance">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-credit-card admin-card-icon"></i>
              Finance Information
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="admin-form-grid admin-form-grid-2">
              <div class="admin-info-group">
                <label class="admin-info-label">Bank Name</label>
                <div class="admin-info-value">{{ company.finance.bankName || 'Not provided' }}</div>
              </div>
              <div class="admin-info-group">
                <label class="admin-info-label">Bank Account</label>
                <div class="admin-info-value admin-info-value-code">{{ company.finance.bankAccount || 'Not provided' }}</div>
              </div>
              <div class="admin-info-group">
                <label class="admin-info-label">Bank Code</label>
                <div class="admin-info-value admin-info-value-code">{{ company.finance.bankCode || 'Not provided' }}</div>
              </div>
              <div class="admin-info-group">
                <label class="admin-info-label">Tax ID</label>
                <div class="admin-info-value admin-info-value-code">{{ company.finance.taxId || 'Not provided' }}</div>
              </div>
              <div class="admin-info-group admin-info-group-full" v-if="company.finance.paymentDetails">
                <label class="admin-info-label">Payment Details</label>
                <div class="admin-info-value admin-info-value-multiline">{{ company.finance.paymentDetails }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Schedule Information -->
        <div class="admin-card admin-card-schedule" v-if="company.schedule && company.schedule.length > 0">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-clock admin-card-icon"></i>
              Schedule Information
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="table-container">
              <table class="admin-table">
                <thead>
                  <tr>
                    <th>Day</th>
                    <th>Open Time</th>
                    <th>Close Time</th>
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="schedule in company.schedule" :key="schedule.day">
                    <td><strong>{{ getDayName(schedule.day) }}</strong></td>
                    <td>{{ schedule.isClosed ? '-' : (schedule.openTime || 'Not set') }}</td>
                    <td>{{ schedule.isClosed ? '-' : (schedule.closeTime || 'Not set') }}</td>
                    <td>
                      <span class="admin-badge" :class="schedule.isClosed ? 'admin-badge-danger' : 'admin-badge-success'">
                        {{ schedule.isClosed ? 'Closed' : 'Open' }}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Meta Information -->
        <div class="admin-card admin-card-meta" v-if="company.meta">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-tags admin-card-icon"></i>
              Meta Information
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="admin-form-grid admin-form-grid-2">
              <div class="admin-info-group">
                <label class="admin-info-label">Meta Title</label>
                <div class="admin-info-value">{{ company.meta.title || 'Not provided' }}</div>
              </div>
              <div class="admin-info-group">
                <label class="admin-info-label">Meta Keywords</label>
                <div class="admin-info-value">{{ company.meta.keywords || 'Not provided' }}</div>
              </div>
              <div class="admin-info-group admin-info-group-full">
                <label class="admin-info-label">Meta Description</label>
                <div class="admin-info-value admin-info-value-multiline">{{ company.meta.description || 'Not provided' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="admin-card admin-card-actions">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-cogs admin-card-icon"></i>
              Actions
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="admin-actions-grid">
              <button
                class="admin-btn admin-btn-success admin-btn-block"
                v-if="!company.isApproved"
                @click="approveCompany"
                :disabled="actionLoading">
                <i class="fas fa-check" :class="{ 'fa-spin': actionLoading }"></i>
                <span>{{ actionLoading ? 'Approving...' : 'Approve Company' }}</span>
              </button>

              <button
                class="admin-btn admin-btn-danger admin-btn-block"
                v-if="!company.isApproved"
                @click="showRejectModal = true"
                :disabled="actionLoading">
                <i class="fas fa-times"></i>
                <span>Reject Company</span>
              </button>

              <router-link
                :to="`/admin/companies/${company.id}/edit`"
                class="admin-btn admin-btn-primary admin-btn-block">
                <i class="fas fa-edit"></i>
                <span>Edit Company</span>
              </router-link>

              <button
                class="admin-btn admin-btn-warning admin-btn-block"
                @click="suspendCompany"
                :disabled="actionLoading">
                <i class="fas fa-pause"></i>
                <span>Suspend Company</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Reject Modal -->
    <div class="modal" :class="{ 'is-active': showRejectModal }">
      <div class="modal-background" @click="showRejectModal = false"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">Reject Company</p>
          <button class="delete" @click="showRejectModal = false"></button>
        </header>
        <section class="modal-card-body">
          <div class="field">
            <label class="label">Rejection Reason</label>
            <div class="control">
              <textarea
                class="textarea"
                v-model="rejectionReason"
                placeholder="Enter reason for rejection...">
              </textarea>
            </div>
          </div>
        </section>
        <footer class="modal-card-foot">
          <button
            class="button is-danger"
            @click="rejectCompany"
            :class="{ 'is-loading': actionLoading }">
            Reject Company
          </button>
          <button class="button" @click="showRejectModal = false">Cancel</button>
        </footer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { companiesService } from '@/admin/services/companies';

const route = useRoute();

// Reactive data
const company = ref({});
const loading = ref(false);
const error = ref(null);
const actionLoading = ref(false);
const showRejectModal = ref(false);
const rejectionReason = ref('');

// Methods
const fetchCompany = async () => {
  loading.value = true;
  error.value = null;

  try {
    const response = await companiesService.getDetailedCompany(route.params.id);
    company.value = response.data;
  } catch (err) {
    error.value = err.message || 'Failed to load company details';
  } finally {
    loading.value = false;
  }
};

const approveCompany = async () => {
  actionLoading.value = true;
  try {
    await companiesService.approveCompany(company.value.id);
    await fetchCompany(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to approve company';
  } finally {
    actionLoading.value = false;
  }
};

const rejectCompany = async () => {
  if (!rejectionReason.value.trim()) {
    error.value = 'Please provide a rejection reason';
    return;
  }

  actionLoading.value = true;
  try {
    await companiesService.rejectCompany(company.value.id, rejectionReason.value);
    showRejectModal.value = false;
    rejectionReason.value = '';
    await fetchCompany(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to reject company';
  } finally {
    actionLoading.value = false;
  }
};

const suspendCompany = async () => {
  actionLoading.value = true;
  try {
    await companiesService.suspendCompany(company.value.id);
    await fetchCompany(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to suspend company';
  } finally {
    actionLoading.value = false;
  }
};

// Utility methods
const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleString();
};

const getDayName = (dayNumber) => {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  const dayIndex = typeof dayNumber === 'string' ? parseInt(dayNumber, 10) : dayNumber;

  if (dayIndex >= 1 && dayIndex <= 7) {
    const adjustedIndex = dayIndex === 7 ? 0 : dayIndex;
    return days[adjustedIndex] || 'Unknown';
  } else if (dayIndex >= 0 && dayIndex <= 6) {
    return days[dayIndex] || 'Unknown';
  }

  return 'Unknown';
};

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString();
};

const formatStatus = (status) => {
  return status?.charAt(0).toUpperCase() + status?.slice(1) || 'Unknown';
};

const formatRole = (role) => {
  return role?.charAt(0).toUpperCase() + role?.slice(1) || 'Unknown';
};

const getStatusClass = (isApproved) => {
  return isApproved ? 'is-success' : 'is-warning';
};

const getRoleClass = (role) => {
  switch (role?.toLowerCase()) {
    case 'owner': return 'is-primary';
    case 'admin': return 'is-info';
    case 'member': return 'is-light';
    default: return 'is-light';
  }
};

const getDayName = (dayNumber) => {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  return days[dayNumber] || 'Unknown';
};

// Lifecycle
onMounted(() => {
  fetchCompany();
});
</script>

<style scoped>
/* Company Detail Layout */
.admin-company-detail {
  padding: var(--admin-spacing-4);
}

.admin-company-content {
  margin-top: var(--admin-spacing-4);
}

/* Grid Layout */
.admin-grid-company {
  display: grid;
  grid-template-columns: 2fr 1fr;
  grid-template-areas:
    "main status"
    "finance finance"
    "schedule schedule"
    "meta meta"
    "actions actions";
  gap: var(--admin-spacing-4);
}

.admin-card-main {
  grid-area: main;
}

.admin-card-status {
  grid-area: status;
}

.admin-card-finance {
  grid-area: finance;
}

.admin-card-schedule {
  grid-area: schedule;
}

.admin-card-meta {
  grid-area: meta;
}

.admin-card-actions {
  grid-area: actions;
}

/* Info Groups */
.admin-info-group {
  margin-bottom: var(--admin-spacing-3);
}

.admin-info-group-full {
  grid-column: span 2;
}

.admin-info-label {
  display: block;
  font-weight: 600;
  color: var(--admin-text-secondary);
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--admin-spacing-1);
}

.admin-info-value {
  color: var(--admin-text-primary);
  font-weight: 500;
  line-height: 1.4;
}

.admin-info-value-multiline {
  line-height: 1.6;
  white-space: pre-wrap;
}

.admin-info-value-code {
  font-family: var(--admin-font-mono);
  background: var(--admin-gray-50);
  padding: var(--admin-spacing-1) var(--admin-spacing-2);
  border-radius: var(--admin-radius-sm);
  font-size: 0.9rem;
}

/* Status Section */
.admin-status-section {
  display: flex;
  flex-direction: column;
  gap: var(--admin-spacing-3);
}

/* Responsive */
@media (max-width: 1024px) {
  .admin-grid-company {
    grid-template-columns: 1fr;
    grid-template-areas:
      "main"
      "status"
      "finance"
      "schedule"
      "meta"
      "actions";
  }
}

@media (max-width: 768px) {
  .admin-company-detail {
    padding: var(--admin-spacing-3);
  }

  .admin-form-grid-2 {
    grid-template-columns: 1fr;
  }

  .admin-info-group-full {
    grid-column: span 1;
  }

  .admin-header-content {
    flex-direction: column;
    gap: var(--admin-spacing-3);
  }

  .admin-header-right {
    width: 100%;
    justify-content: flex-start;
  }
}

/* Table Responsive */
.table-container {
  overflow-x: auto;
  border-radius: var(--admin-radius-md);
  border: 1px solid var(--admin-border-color);
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--admin-white);
}

.admin-table th,
.admin-table td {
  padding: var(--admin-spacing-3);
  text-align: left;
  border-bottom: 1px solid var(--admin-border-color);
}

.admin-table th {
  background: var(--admin-gray-50);
  font-weight: 600;
  color: var(--admin-text-primary);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-table tr:last-child td {
  border-bottom: none;
}

.admin-table tr:hover {
  background: var(--admin-gray-25);
}

/* Actions Grid */
.admin-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--admin-spacing-3);
}

.admin-btn-block {
  width: 100%;
  justify-content: center;
}

@media (max-width: 768px) {
  .admin-actions-grid {
    grid-template-columns: 1fr;
  }
}
</style>
