<template>
  <div class="admin-company-detail">
    <!-- <PERSON> Header -->
    <div class="admin-page-header">
      <div class="admin-header-content">
        <div class="admin-header-left">
          <nav class="admin-breadcrumb">
            <router-link to="/admin/companies" class="admin-breadcrumb-item">
              <i class="fas fa-building"></i>
              Companies
            </router-link>
            <span class="admin-breadcrumb-separator">/</span>
            <span class="admin-breadcrumb-item admin-breadcrumb-current">
              {{ company.name || 'Company Details' }}
            </span>
          </nav>
          <h1 class="admin-page-title">
            <i class="fas fa-building"></i>
            {{ company.name || 'Company Details' }}
          </h1>
        </div>
        <div class="admin-header-right">
          <button class="admin-btn admin-btn-secondary" @click="fetchCompany" :disabled="loading">
            <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
            <span>Refresh</span>
          </button>
          <router-link
            v-if="company.id"
            :to="`/admin/companies/${company.id}/edit`"
            class="admin-btn admin-btn-primary">
            <i class="fas fa-edit"></i>
            <span>Edit Company</span>
          </router-link>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div class="admin-loading-state" v-if="loading && !company.id">
      <div class="admin-spinner">
        <i class="fas fa-spinner fa-pulse"></i>
      </div>
      <p class="admin-loading-text">Loading company details...</p>
    </div>

    <!-- Error State -->
    <div class="admin-alert admin-alert-danger" v-else-if="error">
      <div class="admin-alert-icon">
        <i class="fas fa-exclamation-circle"></i>
      </div>
      <div class="admin-alert-content">
        <div class="admin-alert-message">{{ error }}</div>
      </div>
      <button class="admin-alert-close" @click="error = null">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Company Details -->
    <div v-else-if="company.id" class="admin-company-content">
      <div class="admin-company-layout">

        <!-- Company Information Card -->
        <CompanyInfoCard
          :company="company"
          class="admin-company-section"
        />

        <!-- Finance Information Card -->
        <CompanyFinanceCard
          v-if="company.finance"
          :finance="company.finance"
          class="admin-company-section"
        />

        <!-- Schedule Information -->
        <CompanyScheduleTable
          v-if="company.schedule && company.schedule.length > 0"
          :schedule="company.schedule"
          class="admin-company-section"
        />

        <!-- Company Users -->
        <CompanyUsersTable
          :companyId="company.id"
          class="admin-company-section"
        />

        <!-- Company Products -->
        <CompanyProductsTable
          :companyId="company.id"
          class="admin-company-section"
        />

        <!-- Status & Actions Card -->
        <div class="admin-card admin-company-section">
          <div class="admin-card-header">
            <h3 class="admin-card-title">
              <i class="fas fa-cog admin-card-icon"></i>
              Status & Actions
            </h3>
          </div>
          <div class="admin-card-content">
            <div class="admin-status-grid">
              <div class="admin-status-item">
                <label class="admin-status-label">Approval Status</label>
                <span class="admin-badge" :class="company.isApproved ? 'admin-badge-success' : 'admin-badge-warning'">
                  {{ company.isApproved ? 'Approved' : 'Pending' }}
                </span>
              </div>

              <div class="admin-status-item" v-if="company.isFeatured">
                <label class="admin-status-label">Featured</label>
                <span class="admin-badge admin-badge-info">Featured Company</span>
              </div>

              <div class="admin-status-item" v-if="company.approvedAt">
                <label class="admin-status-label">Approved At</label>
                <span class="admin-status-value">{{ formatDateTime(company.approvedAt) }}</span>
              </div>

              <div class="admin-status-item" v-if="company.approvedByUserId">
                <label class="admin-status-label">Approved By</label>
                <span class="admin-status-value">User ID: {{ company.approvedByUserId }}</span>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="admin-actions-grid">
              <router-link
                :to="`/admin/companies/${company.id}/edit`"
                class="admin-btn admin-btn-primary">
                <i class="fas fa-edit"></i>
                <span>Edit Company</span>
              </router-link>

              <button
                v-if="!company.isApproved"
                class="admin-btn admin-btn-success"
                @click="approveCompany"
                :disabled="actionLoading">
                <i class="fas fa-check"></i>
                <span>Approve Company</span>
              </button>

              <button
                v-if="!company.isApproved"
                class="admin-btn admin-btn-danger"
                @click="showRejectModal = true"
                :disabled="actionLoading">
                <i class="fas fa-times"></i>
                <span>Reject Company</span>
              </button>

              <button
                class="admin-btn admin-btn-warning"
                @click="suspendCompany"
                :disabled="actionLoading">
                <i class="fas fa-pause"></i>
                <span>Suspend Company</span>
              </button>
            </div>
          </div>
        </div>

      </div>
    </div>

    <!-- Rejection Modal -->
    <div v-if="showRejectModal" class="admin-modal-overlay" @click="showRejectModal = false">
      <div class="admin-modal" @click.stop>
        <div class="admin-modal-header">
          <h3 class="admin-modal-title">Reject Company</h3>
          <button class="admin-modal-close" @click="showRejectModal = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="admin-modal-content">
          <div class="admin-form-group">
            <label class="admin-form-label">Rejection Reason *</label>
            <textarea
              v-model="rejectionReason"
              class="admin-form-control"
              rows="4"
              placeholder="Please provide a reason for rejection..."
              required>
            </textarea>
          </div>
        </div>
        <div class="admin-modal-footer">
          <button class="admin-btn admin-btn-secondary" @click="showRejectModal = false">
            Cancel
          </button>
          <button
            class="admin-btn admin-btn-danger"
            @click="rejectCompany"
            :disabled="!rejectionReason.trim() || actionLoading">
            <i class="fas fa-times"></i>
            Reject Company
          </button>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { companiesService } from '@/admin/services/companies';

// Import components
import CompanyInfoCard from '@/admin/components/companies/CompanyInfoCard.vue';
import CompanyFinanceCard from '@/admin/components/companies/CompanyFinanceCard.vue';
import CompanyScheduleTable from '@/admin/components/companies/CompanyScheduleTable.vue';
import CompanyUsersTable from '@/admin/components/companies/CompanyUsersTable.vue';
import CompanyProductsTable from '@/admin/components/companies/CompanyProductsTable.vue';

const route = useRoute();

// Reactive data
const company = ref({});
const loading = ref(false);
const error = ref(null);
const actionLoading = ref(false);
const showRejectModal = ref(false);
const rejectionReason = ref('');

// Methods
const fetchCompany = async () => {
  loading.value = true;
  error.value = null;

  try {
    const response = await companiesService.getDetailedCompany(route.params.id);
    company.value = response.data;
  } catch (err) {
    error.value = err.message || 'Failed to load company details';
  } finally {
    loading.value = false;
  }
};

const approveCompany = async () => {
  actionLoading.value = true;
  try {
    await companiesService.approveCompany(company.value.id);
    await fetchCompany(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to approve company';
  } finally {
    actionLoading.value = false;
  }
};

const rejectCompany = async () => {
  if (!rejectionReason.value.trim()) {
    error.value = 'Please provide a rejection reason';
    return;
  }

  actionLoading.value = true;
  try {
    await companiesService.rejectCompany(company.value.id, rejectionReason.value);
    showRejectModal.value = false;
    rejectionReason.value = '';
    await fetchCompany(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to reject company';
  } finally {
    actionLoading.value = false;
  }
};

const suspendCompany = async () => {
  if (!confirm('Are you sure you want to suspend this company?')) {
    return;
  }

  actionLoading.value = true;
  try {
    // Implement suspend logic here
    await fetchCompany(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to suspend company';
  } finally {
    actionLoading.value = false;
  }
};

const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleString();
};

// Initialize
onMounted(() => {
  fetchCompany();
});
</script>

<style scoped>
.admin-company-layout {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: 1fr;
}

.admin-company-section {
  margin-bottom: 0;
}

.admin-status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.admin-status-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.admin-status-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--admin-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-status-value {
  font-size: 0.875rem;
  color: var(--admin-text-primary);
}

.admin-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
}

@media (min-width: 768px) {
  .admin-company-layout {
    grid-template-columns: 2fr 1fr;
  }

  .admin-company-section:nth-child(1),
  .admin-company-section:nth-child(2),
  .admin-company-section:nth-child(3) {
    grid-column: 1;
  }

  .admin-company-section:nth-child(4),
  .admin-company-section:nth-child(5),
  .admin-company-section:nth-child(6) {
    grid-column: 2;
  }
}

@media (min-width: 1200px) {
  .admin-company-layout {
    grid-template-columns: 1fr 1fr 1fr;
  }

  .admin-company-section:nth-child(1) {
    grid-column: 1 / 3;
  }

  .admin-company-section:nth-child(2),
  .admin-company-section:nth-child(3) {
    grid-column: 3;
  }

  .admin-company-section:nth-child(4),
  .admin-company-section:nth-child(5),
  .admin-company-section:nth-child(6) {
    grid-column: 1 / 4;
  }
}
</style>
