<template>
  <div class="admin-page">
    <!-- Page Header -->
    <div class="admin-page-header">
      <div class="admin-page-title-section">
        <button @click="goBack" class="admin-btn admin-btn-ghost admin-btn-sm">
          <i class="fas fa-arrow-left"></i>
          Back to Products
        </button>
        <h1 class="admin-page-title">
          <i class="fas fa-box-open admin-page-icon"></i>
          {{ isCreate ? 'Create Product' : 'Edit Product' }}
        </h1>
        <p class="admin-page-subtitle">{{ isCreate ? 'Add a new product to your catalog' : 'Update product information and settings' }}</p>
      </div>
      <div class="admin-page-actions">
        <button type="button" @click="resetForm" class="admin-btn admin-btn-secondary">
          <i class="fas fa-undo"></i>
          Reset
        </button>
        <button type="button" @click="$emit('cancel')" class="admin-btn admin-btn-ghost">
          <i class="fas fa-times"></i>
          Cancel
        </button>
        <button @click="handleSubmit" :disabled="saving" class="admin-btn admin-btn-primary">
          <i class="fas fa-save" :class="{ 'fa-spinner fa-pulse': saving }"></i>
          {{ saving ? 'Saving...' : (isCreate ? 'Create Product' : 'Save Changes') }}
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="admin-loading-state">
      <div class="admin-spinner">
        <i class="fas fa-spinner fa-pulse"></i>
      </div>
      <p class="admin-loading-text">Loading product data...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="admin-alert admin-alert-danger">
      <i class="fas fa-exclamation-triangle"></i>
      <div>
        <strong>Error loading product</strong>
        <p>{{ error }}</p>
      </div>
      <button @click="loadProduct" class="admin-btn admin-btn-sm admin-btn-danger">
        <i class="fas fa-retry"></i>
        Retry
      </button>
    </div>

    <!-- Edit Form -->
    <div v-else class="admin-content">
      <form @submit.prevent="handleSubmit" class="admin-form">

      <!-- Form Content - Sequential Layout -->
      <div class="form-sections">
        <!-- Basic Information -->
        <div class="box mb-5">
          <h2 class="title is-4 mb-5">
            <span class="icon-text">
              <span class="icon has-text-primary">
                <i class="fas fa-info-circle"></i>
              </span>
              <span>Basic Information</span>
            </span>
          </h2>
          <div class="columns is-multiline">
            <div class="column is-6">
              <CompanySelect
                v-model="form.companyId"
                label="Company"
                placeholder="Search and select company..."
                :required="true"
                @change="onCompanyChange"
              />
            </div>
            <div class="column is-6">
              <CategorySelect
                v-model="form.categoryId"
                label="Category"
                placeholder="Search and select category..."
                :required="true"
                @change="onCategoryChange"
              />
            </div>
            <div class="column is-12">
              <div class="field">
                <label class="label">Product Name *</label>
                <div class="control">
                  <input v-model="form.name" class="input" type="text"
                         placeholder="Enter product name" required>
                </div>
              </div>
            </div>
            <div class="column is-12">
              <div class="field">
                <label class="label">Slug</label>
                <div class="control">
                  <input v-model="form.slug" class="input" type="text"
                         placeholder="Auto-generated from name">
                </div>
                <p class="help">Leave empty to auto-generate from product name</p>
              </div>
            </div>
            <div class="column is-12">
              <div class="field">
                <label class="label">Description</label>
                <div class="control">
                  <textarea v-model="form.description" class="textarea"
                            placeholder="Enter product description" rows="4"></textarea>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Product Images Manager -->
        <div class="box mb-5">
          <h2 class="title is-4 mb-5">
            <span class="icon-text">
              <span class="icon has-text-link">
                <i class="fas fa-images"></i>
              </span>
              <span>Product Images</span>
            </span>
          </h2>
          <ProductImageManager
            ref="imageManager"
            :product-id="currentProductId"
            :images="productImages"
            :is-create="props.isCreate"
            @images-updated="loadProductImages"
            @main-image-changed="handleMainImageChanged"
            @image-uploaded="handleImageUploaded"
            @image-deleted="handleImageDeleted"
            @pending-images-changed="handlePendingImagesChanged"
          />
        </div>

        <!-- Pricing & Inventory -->
        <div class="box mb-5">
          <h2 class="title is-4 mb-5">
            <span class="icon-text">
              <span class="icon has-text-success">
                <i class="fas fa-dollar-sign"></i>
              </span>
              <span>Pricing & Inventory</span>
            </span>
          </h2>
          <div class="columns">
            <div class="column is-4">
              <div class="field">
                <label class="label">Price *</label>
                <div class="control has-icons-left">
                  <input v-model.number="form.priceAmount" class="input" type="number"
                         step="0.01" placeholder="0.00" required>
                  <span class="icon is-small is-left">
                    <i class="fas fa-hryvnia-sign"></i>
                  </span>
                </div>
              </div>
            </div>
            <div class="column is-4">
              <div class="field">
                <label class="label">Stock *</label>
                <div class="control has-icons-left">
                  <input v-model.number="form.stock" class="input" type="number"
                         min="0" placeholder="0" required>
                  <span class="icon is-small is-left">
                    <i class="fas fa-boxes"></i>
                  </span>
                </div>
              </div>
            </div>
            <div class="column is-4">
              <div class="field">
                <label class="label">Status</label>
                <div class="control">
                  <div class="select is-fullwidth">
                    <select v-model="form.status">
                      <option value="0">Pending</option>
                      <option value="1">Approved</option>
                      <option value="2">Rejected</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Product Attributes -->
        <div class="box mb-5">
          <h2 class="title is-4 mb-5">
            <span class="icon-text">
              <span class="icon has-text-info">
                <i class="fas fa-tags"></i>
              </span>
              <span>Product Attributes</span>
            </span>
          </h2>

          <!-- Add New Attribute -->
          <div class="field is-grouped mb-4">
            <div class="control is-expanded">
              <input v-model="newAttribute.key" class="input" type="text"
                     placeholder="Attribute name (e.g., Color, Size)">
            </div>
            <div class="control is-expanded">
              <input v-model="newAttribute.value" class="input" type="text"
                     placeholder="Attribute value (e.g., Red, Large)">
            </div>
            <div class="control">
              <button type="button" class="button is-primary" @click="addAttribute">
                <span class="icon">
                  <i class="fas fa-plus"></i>
                </span>
                <span>Add</span>
              </button>
            </div>
          </div>

          <!-- Attributes Table -->
          <div v-if="Object.keys(form.attributes).length > 0" class="table-container">
            <table class="table is-fullwidth is-striped is-hoverable">
              <thead>
                <tr>
                  <th>Attribute</th>
                  <th>Values</th>
                  <th width="100">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(values, key) in form.attributes" :key="key">
                  <td><strong>{{ key }}</strong></td>
                  <td>
                    <span class="tags">
                      <span v-for="(value, index) in (Array.isArray(values) ? values : [values])"
                            :key="index" class="tag is-light">
                        {{ value }}
                        <button type="button" class="delete is-small"
                                @click="removeAttributeValue(key, index)"></button>
                      </span>
                    </span>
                  </td>
                  <td>
                    <button type="button" class="button is-small is-danger"
                            @click="removeAttribute(key)">
                      <span class="icon">
                        <i class="fas fa-trash"></i>
                      </span>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div v-else class="notification is-light has-text-centered">
            <span class="icon-text">
              <span class="icon">
                <i class="fas fa-info-circle"></i>
              </span>
              <span>No attributes added yet. Use the form above to add product attributes.</span>
            </span>
          </div>
        </div>

        <!-- SEO Meta -->
        <div class="box mb-5">
          <h2 class="title is-4 mb-5">
            <span class="icon-text">
              <span class="icon has-text-warning">
                <i class="fas fa-search"></i>
              </span>
              <span>SEO Meta Information</span>
            </span>
          </h2>
          <div class="columns is-multiline">
            <div class="column is-12">
              <div class="field">
                <label class="label">Meta Title</label>
                <div class="control">
                  <input v-model="form.metaTitle" class="input" type="text"
                         placeholder="SEO title for search engines">
                </div>
              </div>
            </div>
            <div class="column is-12">
              <div class="field">
                <label class="label">Meta Description</label>
                <div class="control">
                  <textarea v-model="form.metaDescription" class="textarea"
                            placeholder="SEO description for search engines" rows="3"></textarea>
                </div>
              </div>
            </div>
            <div class="column is-12">
              <div class="field">
                <label class="label">Meta Image</label>
                <div class="field has-addons">
                  <div class="control is-expanded">
                    <div class="file has-name is-fullwidth">
                      <label class="file-label">
                        <input class="file-input" type="file" accept="image/*" @change="handleImageUpload" :disabled="uploadingImage">
                        <span class="file-cta" :class="{ 'is-loading': uploadingImage }">
                          <span class="file-icon" v-if="!uploadingImage">
                            <i class="fas fa-upload"></i>
                          </span>
                          <span class="file-label">{{ uploadingImage ? 'Uploading...' : 'Choose image...' }}</span>
                        </span>
                        <span class="file-name">{{ imageFileName || 'No file selected' }}</span>
                      </label>
                    </div>
                  </div>
                  <div v-if="form.metaImage" class="control">
                    <button type="button" class="button is-danger" @click="handleDeleteMetaImage" :disabled="uploadingImage">
                      <span class="icon">
                        <i class="fas fa-trash"></i>
                      </span>
                      <span>Clear</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="form.metaImage" class="has-text-centered mt-4">
            <div class="box has-background-light p-4">
              <figure class="image is-128x128 is-inline-block">
                <img :src="form.metaImage" :alt="form.name" class="is-rounded">
              </figure>
              <p class="has-text-grey mt-2">
                <small>Meta Image Preview</small>
              </p>
            </div>
          </div>
        </div>



        <!-- Timestamps (Read-only for edit) -->
        <div v-if="!isCreate" class="box mb-5">
          <h2 class="title is-4 mb-5">
            <span class="icon-text">
              <span class="icon has-text-grey">
                <i class="fas fa-clock"></i>
              </span>
              <span>Timestamps</span>
            </span>
          </h2>
          <div class="columns">
            <div class="column is-6">
              <div class="field">
                <label class="label">Created At</label>
                <div class="control">
                  <input class="input" type="text" :value="formatDate(form.createdAt)" readonly>
                </div>
              </div>
            </div>
            <div v-if="form.updatedAt" class="column is-6">
              <div class="field">
                <label class="label">Updated At</label>
                <div class="control">
                  <input class="input" type="text" :value="formatDate(form.updatedAt)" readonly>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { productsService } from '@/admin/services/products';
import CategorySelect from '@/admin/components/common/CategorySelect.vue';
import CompanySelect from '@/admin/components/common/CompanySelect.vue';
import ProductImageManager from './ProductImageManager.vue';
import api from '@/services/api';

// Props
const props = defineProps({
  productId: {
    type: String,
    required: false
  },
  isCreate: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['save', 'cancel']);

// Route
const route = useRoute();
const router = useRouter();

// State
const loading = ref(false);
const saving = ref(false);
const uploadingImage = ref(false);
const error = ref(null);
const imageFileName = ref('');
const productImages = ref([]);
const imageManager = ref(null); // Reference to ProductImageManager component
const originalMetaImage = ref(''); // Зберігаємо оригінальне MetaImage для порівняння

// Form data
const form = ref({
  id: '',
  companyId: '',
  name: '',
  slug: '',
  description: '',
  priceAmount: 0,
  priceCurrency: 0, // 0 = UAH, 1 = USD, 2 = EUR
  stock: 0,
  status: '0', // 0 = Pending, 1 = Approved, 2 = Rejected
  categoryId: '',
  attributes: {},
  metaTitle: '',
  metaDescription: '',
  metaImage: '',
  createdAt: null,
  updatedAt: null
});

// New attribute form
const newAttribute = ref({
  key: '',
  value: ''
});

// Get product ID from props or route
const currentProductId = computed(() => {
  return props.productId || route.params.id;
});



// Methods
const addAttribute = () => {
  if (!newAttribute.value.key.trim() || !newAttribute.value.value.trim()) {
    return;
  }
  
  const key = newAttribute.value.key.trim();
  const value = newAttribute.value.value.trim();
  
  if (form.value.attributes[key]) {
    // If attribute exists, add to array or convert to array
    if (Array.isArray(form.value.attributes[key])) {
      form.value.attributes[key].push(value);
    } else {
      form.value.attributes[key] = [form.value.attributes[key], value];
    }
  } else {
    // New attribute
    form.value.attributes[key] = value;
  }
  
  // Clear form
  newAttribute.value.key = '';
  newAttribute.value.value = '';
};

const removeAttribute = (key) => {
  delete form.value.attributes[key];
};

const removeAttributeValue = (key, index) => {
  const values = form.value.attributes[key];
  if (Array.isArray(values)) {
    if (values.length === 1) {
      delete form.value.attributes[key];
    } else {
      values.splice(index, 1);
    }
  } else {
    delete form.value.attributes[key];
  }
};

const handleImageUpload = async (event) => {
  const file = event.target.files[0];
  if (file) {
    imageFileName.value = file.name;
    uploadingImage.value = true;

    try {
      // Завжди тільки показуємо превью, не завантажуємо на сервер
      // Завантаження буде відбуватися при збереженні форми
      const reader = new FileReader();
      reader.onload = (e) => {
        form.value.metaImage = e.target.result;
      };
      reader.readAsDataURL(file);
    } catch (err) {
      console.error('Error processing meta image:', err);
      error.value = 'Failed to process meta image. Please try again.';
    } finally {
      uploadingImage.value = false;
    }
  }
};

const handleDeleteMetaImage = () => {
  // Просто очищуємо превью, фактичне видалення буде при збереженні форми
  form.value.metaImage = '';
  imageFileName.value = '';
};

// Функція для завантаження MetaImage
const uploadMetaImage = async (productId) => {
  if (!form.value.metaImage || !form.value.metaImage.startsWith('data:')) {
    return;
  }

  // Конвертуємо data URL в File
  const response = await fetch(form.value.metaImage);
  const blob = await response.blob();
  const file = new File([blob], imageFileName.value || 'meta-image.jpg', { type: blob.type });

  const formData = new FormData();
  formData.append('image', file);

  await api.post(`/api/admin/products/${productId}/meta-image`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

// Event handlers for ProductImageManager
const handleMainImageChanged = (imageId) => {
  console.log('Main image changed:', imageId);
  // The component will handle the API call
};

const handleImageUploaded = (uploadResult) => {
  console.log('Image uploaded:', uploadResult);
  // Reload images to show the new one
  loadProductImages();
};

const handleImageDeleted = (imageId) => {
  console.log('Image deleted:', imageId);
  // The component will handle the API call and emit this event
};

const handlePendingImagesChanged = (pendingImages) => {
  console.log('Pending images changed:', pendingImages.length);
  // Store reference for save operation
};

const loadProductImages = async () => {
  if (props.isCreate || !currentProductId.value) return;

  try {
    const response = await api.get(`/api/admin/products/${currentProductId.value}/with-images`);

    if (response.data && response.data.success && response.data.data) {
      productImages.value = response.data.data.images || [];
    }
  } catch (err) {
    console.error('Error loading product images:', err);
    productImages.value = [];
  }
};



const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  try {
    // Handle backend date format: "2025-06-02 20:35:40.231835+03"
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('uk-UA', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(date);
  } catch (e) {
    console.error('Error formatting date:', e, dateString);
    return dateString;
  }
};

const onCategoryChange = (category) => {
  console.log('Category changed:', category);
};

const onCompanyChange = (company) => {
  console.log('Company changed:', company);
};

const goBack = () => {
  router.push('/admin/products');
};

const resetForm = () => {
  if (isCreate.value) {
    // Reset to empty form for create mode
    form.value = {
      companyId: null,
      name: '',
      description: '',
      categoryId: null,
      priceAmount: 0,
      priceCurrency: 'HRN',
      stockQuantity: 0,
      sku: '',
      slug: '',
      tags: '',
      isActive: true,
      images: []
    };
  } else {
    // Reset to original product data for edit mode
    if (originalProduct.value) {
      form.value = {
        ...originalProduct.value,
        tags: Array.isArray(originalProduct.value.tags)
          ? originalProduct.value.tags.join(', ')
          : originalProduct.value.tags || '',
        images: originalProduct.value.images || []
      };
    }
  }

  // Clear any validation errors
  error.value = null;

  console.log('Form reset:', isCreate.value ? 'to empty' : 'to original data');
};

const handleSubmit = async () => {
  try {
    saving.value = true;
    error.value = null;

    // Validate required fields
    if (!form.value.companyId) {
      error.value = 'Company is required';
      saving.value = false;
      return;
    }

    if (!form.value.name || !form.value.name.trim()) {
      error.value = 'Product name is required';
      saving.value = false;
      return;
    }

    if (!form.value.description || !form.value.description.trim()) {
      error.value = 'Product description is required';
      saving.value = false;
      return;
    }

    if (!form.value.categoryId) {
      error.value = 'Category is required';
      saving.value = false;
      return;
    }

    if (!form.value.priceAmount || form.value.priceAmount <= 0) {
      error.value = 'Price must be greater than 0';
      saving.value = false;
      return;
    }

    // Generate slug if empty
    const generatedSlug = form.value.slug || form.value.name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim('-');

    // Prepare data for API - must match StoreProductCommand exactly
    const productData = {
      companyId: form.value.companyId,
      name: form.value.name.trim(),
      slug: generatedSlug,
      description: form.value.description.trim(),
      priceCurrency: 0, // 0 = UAH, 1 = USD, 2 = EUR (send as enum number)
      priceAmount: parseFloat(form.value.priceAmount) || 0,
      stock: parseInt(form.value.stock) || 0,
      categoryId: form.value.categoryId,
      status: parseInt(form.value.status) || 0, // 0=Pending, 1=Approved, 2=Rejected
      attributes: form.value.attributes || null, // Can be null according to command
      metaTitle: form.value.metaTitle || form.value.name || '',
      metaDescription: form.value.metaDescription || form.value.description || '',
      // MetaImage: для створення - placeholder, для редагування - існуюче значення або placeholder
      metaImage: (form.value.metaImage && !form.value.metaImage.startsWith('data:'))
        ? form.value.metaImage.trim()
        : 'https://via.placeholder.com/300x200.png?text=Product+Image'
    };

    console.log('📤 Sending product data:', productData);

    let productId;
    if (props.isCreate) {
      const result = await productsService.createProduct(productData);
      console.log('✅ Product created:', result);
      productId = result.data || result;

      // Завантажуємо MetaImage якщо це data URL (тобто користувач вибрав файл)
      if (form.value.metaImage && form.value.metaImage.startsWith('data:') && productId) {
        try {
          await uploadMetaImage(productId);
          console.log('✅ Meta image uploaded for new product');
        } catch (metaImageError) {
          console.error('❌ Error uploading meta image:', metaImageError);
          // Не блокуємо збереження продукту через помилку з meta image
        }
      }
    } else {
      const result = await productsService.updateProduct(currentProductId.value, productData);
      console.log('✅ Product updated:', result);
      productId = currentProductId.value;

      // Обробляємо MetaImage при редагуванні
      if (form.value.metaImage && form.value.metaImage.startsWith('data:')) {
        // Користувач вибрав нове зображення - завантажуємо його
        try {
          await uploadMetaImage(productId);
          console.log('✅ Meta image updated');
        } catch (metaImageError) {
          console.error('❌ Error updating meta image:', metaImageError);
        }
      } else if (!form.value.metaImage && originalMetaImage.value) {
        // Користувач очистив MetaImage (було зображення, а тепер немає) - видаляємо його
        try {
          await api.delete(`/api/admin/products/${productId}/meta-image`);
          console.log('✅ Meta image deleted');
        } catch (metaImageError) {
          console.error('❌ Error deleting meta image:', metaImageError);
          // Не блокуємо збереження через помилку видалення
        }
      }
      // Якщо metaImage не змінилося (не data URL і не очищено), нічого не робимо
    }

    // Завантажуємо тимчасові зображення після збереження продукту
    if (imageManager.value && productId) {
      const pendingImages = imageManager.value.getPendingImages();
      if (pendingImages.length > 0) {
        console.log(`🔄 Uploading ${pendingImages.length} pending images...`);
        await imageManager.value.uploadPendingImages(productId);
      }
    }

    emit('save', productData);
  } catch (err) {
    console.error('❌ Error saving product:', err);

    // Extract detailed error message
    let errorMessage = 'Failed to save product';
    if (err.response && err.response.data) {
      if (err.response.data.message) {
        errorMessage = err.response.data.message;
      } else if (err.response.data.errors) {
        errorMessage = Object.values(err.response.data.errors).flat().join(', ');
      } else if (typeof err.response.data === 'string') {
        errorMessage = err.response.data;
      }
    } else if (err.message) {
      errorMessage = err.message;
    }

    error.value = errorMessage;
  } finally {
    saving.value = false;
  }
};

// Auto-generate slug from name
watch(() => form.value.name, (newName) => {
  if (newName && !form.value.slug) {
    form.value.slug = newName
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim('-');
  }
});

// Load data
const loadProduct = async () => {
  if (props.isCreate) return;
  
  try {
    loading.value = true;
    error.value = null;
    
    const response = await productsService.getProductById(currentProductId.value);
    const product = response.data || response;
    
    // Populate form
    Object.keys(form.value).forEach(key => {
      if (product[key] !== undefined) {
        form.value[key] = product[key];
      }
    });

    // Зберігаємо оригінальне MetaImage для порівняння
    originalMetaImage.value = product.metaImage || '';
    
    // Parse attributes
    if (product.attributes) {
      try {
        form.value.attributes = typeof product.attributes === 'string' 
          ? JSON.parse(product.attributes) 
          : product.attributes;
      } catch (e) {
        console.error('Error parsing attributes:', e);
        form.value.attributes = {};
      }
    }
    
    console.log('Product loaded for editing:', form.value);

    // Load product images
    await loadProductImages();
  } catch (err) {
    console.error('Error loading product:', err);
    error.value = err.message || 'Failed to load product';
  } finally {
    loading.value = false;
  }
};





// Initialize
onMounted(async () => {
  await loadProduct();
});
</script>

<style scoped>
.product-edit {
  padding: 1rem;
}

.loader {
  width: 3rem;
  height: 3rem;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.box {
  margin-bottom: 1.5rem;
}

.tags .tag {
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

.image img {
  object-fit: cover;
}
</style>
