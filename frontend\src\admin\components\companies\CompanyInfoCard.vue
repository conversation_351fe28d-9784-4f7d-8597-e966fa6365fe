<template>
  <div class="admin-card">
    <div class="admin-card-header">
      <h3 class="admin-card-title">
        <i class="fas fa-building admin-card-icon"></i>
        Company Information
      </h3>
    </div>
    <div class="admin-card-content">
      <!-- Company Image -->
      <div v-if="displayImageUrl" class="company-image-section">
        <img :src="displayImageUrl" :alt="company.name" class="company-image" />
      </div>

      <!-- Basic Information -->
      <div class="admin-info-grid">
        <div class="admin-info-group">
          <label class="admin-info-label">Company Name</label>
          <div class="admin-info-value">{{ company.name || 'N/A' }}</div>
        </div>
        
        <div class="admin-info-group">
          <label class="admin-info-label">Slug</label>
          <div class="admin-info-value admin-info-value-code">{{ company.slug || 'N/A' }}</div>
        </div>
        
        <div class="admin-info-group">
          <label class="admin-info-label">Contact Email</label>
          <div class="admin-info-value">
            <a v-if="company.contactEmail" :href="`mailto:${company.contactEmail}`" class="admin-link">
              <i class="fas fa-envelope"></i>
              {{ company.contactEmail }}
            </a>
            <span v-else class="admin-text-muted">N/A</span>
          </div>
        </div>
        
        <div class="admin-info-group">
          <label class="admin-info-label">Contact Phone</label>
          <div class="admin-info-value">
            <a v-if="company.contactPhone" :href="`tel:${company.contactPhone}`" class="admin-link">
              <i class="fas fa-phone"></i>
              {{ company.contactPhone }}
            </a>
            <span v-else class="admin-text-muted">N/A</span>
          </div>
        </div>
      </div>

      <!-- Description -->
      <div class="admin-info-group admin-info-group-full">
        <label class="admin-info-label">Description</label>
        <div class="admin-info-value admin-info-value-multiline">
          {{ company.description || 'No description provided' }}
        </div>
      </div>

      <!-- Address -->
      <div class="admin-info-group admin-info-group-full">
        <label class="admin-info-label">Address</label>
        <div class="admin-info-value">
          <div v-if="hasAddress" class="address-display">
            <div v-if="company.addressStreet" class="address-line">
              <i class="fas fa-map-marker-alt"></i>
              {{ company.addressStreet }}
            </div>
            <div v-if="cityRegionPostal" class="address-line">
              <i class="fas fa-city"></i>
              {{ cityRegionPostal }}
            </div>
          </div>
          <span v-else class="admin-text-muted">No address provided</span>
        </div>
      </div>

      <!-- Meta Information -->
      <div v-if="company.metaTitle || company.metaDescription" class="meta-section">
        <h4 class="meta-section-title">
          <i class="fas fa-tags"></i>
          SEO Meta Information
        </h4>
        
        <div class="admin-info-grid">
          <div v-if="company.metaTitle" class="admin-info-group">
            <label class="admin-info-label">Meta Title</label>
            <div class="admin-info-value">{{ company.metaTitle }}</div>
          </div>
          
          <div v-if="company.metaDescription" class="admin-info-group admin-info-group-full">
            <label class="admin-info-label">Meta Description</label>
            <div class="admin-info-value admin-info-value-multiline">{{ company.metaDescription }}</div>
          </div>
          
          <div v-if="company.metaImage" class="admin-info-group">
            <label class="admin-info-label">Meta Image</label>
            <div class="admin-info-value">
              <a :href="company.metaImage" target="_blank" class="admin-link">
                <i class="fas fa-external-link-alt"></i>
                View Image
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Status Badges -->
      <div class="status-badges">
        <span class="admin-badge" :class="company.isApproved ? 'admin-badge-success' : 'admin-badge-warning'">
          <i class="fas" :class="company.isApproved ? 'fa-check' : 'fa-clock'"></i>
          {{ company.isApproved ? 'Approved' : 'Pending Approval' }}
        </span>
        
        <span v-if="company.isFeatured" class="admin-badge admin-badge-info">
          <i class="fas fa-star"></i>
          Featured
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  company: {
    type: Object,
    required: true,
    default: () => ({})
  }
});

const hasAddress = computed(() => {
  return props.company.addressStreet || 
         props.company.addressCity || 
         props.company.addressRegion || 
         props.company.addressPostalCode;
});

const cityRegionPostal = computed(() => {
  const parts = [
    props.company.addressCity,
    props.company.addressRegion,
    props.company.addressPostalCode
  ].filter(Boolean);
  
  return parts.join(', ');
});
</script>

<style scoped>
.company-image-section {
  margin-bottom: 1.5rem;
  text-align: center;
}

.company-image {
  max-width: 200px;
  max-height: 120px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  object-fit: cover;
}

.admin-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.admin-info-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.admin-info-group-full {
  grid-column: 1 / -1;
}

.admin-info-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--admin-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-info-value {
  font-size: 0.9rem;
  color: var(--admin-text-primary);
  line-height: 1.5;
}

.admin-info-value-code {
  font-family: 'Courier New', monospace;
  background: var(--admin-bg-tertiary);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.85rem;
}

.admin-info-value-multiline {
  white-space: pre-wrap;
  word-break: break-word;
}

.admin-link {
  color: var(--admin-link);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: color 0.2s ease;
}

.admin-link:hover {
  color: var(--admin-link-hover);
  text-decoration: underline;
}

.address-display {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.address-line {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.address-line i {
  color: var(--admin-text-muted);
  width: 16px;
}

.meta-section {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--admin-border-light);
}

.meta-section-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--admin-text-secondary);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.meta-section-title i {
  color: var(--admin-text-muted);
}

.status-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--admin-border-light);
}

.admin-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-badge-success {
  background: var(--admin-success);
  color: white;
}

.admin-badge-warning {
  background: var(--admin-warning);
  color: var(--admin-text-primary);
}

.admin-badge-info {
  background: var(--admin-info);
  color: white;
}

.admin-text-muted {
  color: var(--admin-text-muted);
  font-style: italic;
}
</style>
