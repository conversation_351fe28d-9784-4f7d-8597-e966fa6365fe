import api from '@/services/api';

export const companiesService = {
  async getCompanies(params = {}) {
    try {
      console.log('Requesting companies with params:', params);

      // Стандартизуємо параметри для відповідності API
      const apiParams = {};

      // Базові параметри пагінації
      if (params.page) apiParams.page = params.page;
      if (params.pageSize) apiParams.pageSize = params.pageSize;

      // Конвертуємо sortBy/sortOrder в orderBy/descending
      if (params.sortBy) {
        apiParams.orderBy = params.sortBy;
      } else if (params.orderBy) {
        apiParams.orderBy = params.orderBy;
      }

      if (params.sortOrder) {
        apiParams.descending = params.sortOrder === 'desc';
      } else if (params.descending !== undefined) {
        apiParams.descending = params.descending;
      }

      // Пошук - використовуємо 'filter' як очікує API
      if (params.search && params.search.trim() !== '') {
        apiParams.filter = params.search.trim();
      } else if (params.filter && params.filter.trim() !== '') {
        apiParams.filter = params.filter.trim();
      }

      // Статус фільтр видалено - тепер показуємо тільки схвалені компанії

      // Featured фільтр
      if (params.featured && params.featured.trim() !== '' && params.featured !== 'all') {
        apiParams.isFeatured = params.featured === 'true';
      }

      console.log('Final API params for companies:', apiParams);

      // Використовуємо admin endpoint
      const response = await api.get('/api/admin/companies', { params: apiParams });
      console.log('Admin companies API response:', response.data);
      console.log('API params sent:', apiParams);

      // Admin endpoint повертає дані в response.data.data (ApiResponse<PaginatedResponse<CompanyResponse>>)
      if (response.data && response.data.success && response.data.data) {
        console.log('Returning paginated data:', response.data.data);
        console.log('Total items:', response.data.data.total);
        console.log('Current page:', response.data.data.currentPage);
        console.log('Total pages:', response.data.data.lastPage);
        console.log('Items count:', response.data.data.data?.length);
        return response.data.data;
      }

      console.error('Invalid response format:', response.data);
      throw new Error('Invalid response format from server');
    } catch (error) {
      console.error('Error fetching companies:', error);
      throw new Error(error.response?.data?.message || 'Failed to load companies');
    }
  },

  async getPendingCompanies(params = {}) {
    try {
      console.log('Requesting pending companies with params:', params);

      // Стандартизуємо параметри для відповідності API
      const apiParams = {};

      // Базові параметри пагінації
      if (params.page) apiParams.page = params.page;
      if (params.pageSize) apiParams.pageSize = params.pageSize;
      if (params.orderBy) apiParams.orderBy = params.orderBy;
      if (params.descending !== undefined) apiParams.descending = params.descending;

      // Пошук - використовуємо 'filter' як очікує API
      if (params.search && params.search.trim() !== '') {
        apiParams.filter = params.search.trim();
      } else if (params.filter && params.filter.trim() !== '') {
        apiParams.filter = params.filter.trim();
      }

      console.log('Final API params for pending companies:', apiParams);

      const response = await api.get('/api/admin/companies/pending', { params: apiParams });

      console.log('Pending companies API response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching pending companies:', error);
      throw new Error(error.response?.data?.message || 'Failed to load pending companies');
    }
  },

  async getCompanyById(id) {
    try {
      const response = await api.get(`/api/admin/companies/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching company:', error);
      throw new Error(error.response?.data?.message || 'Failed to load company details');
    }
  },

  async approveCompany(id) {
    try {
      const response = await api.post(`/api/admin/companies/${id}/approve`);
      return response.data;
    } catch (error) {
      console.error('Error approving company:', error);
      throw new Error(error.response?.data?.message || 'Failed to approve company');
    }
  },

  async rejectCompany(id, reason = '') {
    try {
      const response = await api.post(`/api/admin/companies/${id}/reject`, { reason });
      return response.data;
    } catch (error) {
      console.error('Error rejecting company:', error);
      throw new Error(error.response?.data?.message || 'Failed to reject company');
    }
  },

  async deleteCompany(id) {
    try {
      const response = await api.delete(`/api/admin/companies/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting company:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete company');
    }
  },

  async getCompany(id) {
    try {
      const response = await api.get(`/api/admin/companies/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error getting company:', error);
      throw new Error(error.response?.data?.message || 'Failed to get company');
    }
  },

  async getDetailedCompany(id) {
    try {
      const response = await api.get(`/api/admin/companies/${id}/detailed`);
      return response.data;
    } catch (error) {
      console.error('Error getting detailed company:', error);
      throw new Error(error.response?.data?.message || 'Failed to get detailed company');
    }
  },

  async updateCompany(id, data) {
    try {
      const response = await api.put(`/api/admin/companies/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating company:', error);
      throw new Error(error.response?.data?.message || 'Failed to update company');
    }
  },

  async updateDetailedCompany(id, data) {
    try {
      const response = await api.put(`/api/admin/companies/${id}/detailed`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating detailed company:', error);
      throw new Error(error.response?.data?.message || 'Failed to update detailed company');
    }
  }
};
