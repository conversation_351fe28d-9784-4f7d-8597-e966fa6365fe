<template>
  <div class="admin-card">
    <div class="admin-card-header">
      <h3 class="admin-card-title">
        <i class="fas fa-users admin-card-icon"></i>
        Company Users
      </h3>
      <div class="admin-card-actions">
        <button class="admin-btn admin-btn-sm admin-btn-secondary" @click="fetchUsers" :disabled="loading">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
        </button>
      </div>
    </div>
    <div class="admin-card-content">
      
      <!-- Loading State -->
      <div v-if="loading" class="loading-state">
        <i class="fas fa-spinner fa-pulse"></i>
        <span>Loading users...</span>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-state">
        <i class="fas fa-exclamation-triangle"></i>
        <span>{{ error }}</span>
      </div>

      <!-- Empty State -->
      <div v-else-if="!users.length" class="empty-state">
        <i class="fas fa-user-slash"></i>
        <span>No users found for this company</span>
      </div>

      <!-- Users Table -->
      <div v-else class="users-table-wrapper">
        <table class="admin-table users-table">
          <thead>
            <tr>
              <th>User</th>
              <th>Role</th>
              <th>Status</th>
              <th>Joined</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="user in users" :key="user.id" class="user-row">
              <td class="user-cell">
                <div class="user-info">
                  <div class="user-avatar">
                    <i class="fas fa-user"></i>
                  </div>
                  <div class="user-details">
                    <div class="user-name">{{ user.username || 'N/A' }}</div>
                    <div class="user-email">{{ user.email || 'N/A' }}</div>
                  </div>
                </div>
              </td>
              <td class="role-cell">
                <span class="role-badge" :class="getRoleClass(user.role)">
                  <i class="fas" :class="getRoleIcon(user.role)"></i>
                  {{ formatRole(user.role) }}
                </span>
              </td>
              <td class="status-cell">
                <span class="status-badge" :class="getStatusClass(user.isApproved)">
                  <i class="fas" :class="getStatusIcon(user.isApproved)"></i>
                  {{ user.isApproved ? 'Active' : 'Pending' }}
                </span>
              </td>
              <td class="date-cell">
                <div class="date-info">
                  <div class="date-primary">{{ formatDate(user.createdAt) }}</div>
                  <div class="date-secondary">{{ formatTime(user.createdAt) }}</div>
                </div>
              </td>
              <td class="actions-cell">
                <div class="action-buttons">
                  <router-link 
                    :to="`/admin/users/${user.id}`"
                    class="admin-btn admin-btn-xs admin-btn-primary"
                    title="View User">
                    <i class="fas fa-eye"></i>
                  </router-link>
                  <button 
                    class="admin-btn admin-btn-xs admin-btn-secondary"
                    @click="editUserRole(user)"
                    title="Edit Role">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button 
                    v-if="user.role !== 'Owner'"
                    class="admin-btn admin-btn-xs admin-btn-danger"
                    @click="removeUser(user)"
                    title="Remove User">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Summary -->
      <div v-if="users.length" class="users-summary">
        <div class="summary-item">
          <span class="summary-label">Total Users:</span>
          <span class="summary-value">{{ users.length }}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Active:</span>
          <span class="summary-value">{{ activeUsersCount }}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Pending:</span>
          <span class="summary-value">{{ pendingUsersCount }}</span>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { companiesService } from '@/admin/services/companies';

const props = defineProps({
  companyId: {
    type: String,
    required: true
  }
});

// Reactive data
const users = ref([]);
const loading = ref(false);
const error = ref(null);

// Computed properties
const activeUsersCount = computed(() => {
  return users.value.filter(user => user.isApproved).length;
});

const pendingUsersCount = computed(() => {
  return users.value.filter(user => !user.isApproved).length;
});

// Methods
const fetchUsers = async () => {
  loading.value = true;
  error.value = null;

  try {
    const response = await companiesService.getCompanyUsers(props.companyId);
    users.value = response.data.data || [];
  } catch (err) {
    error.value = err.message || 'Failed to load company users';
    users.value = [];
  } finally {
    loading.value = false;
  }
};

const formatRole = (role) => {
  if (!role) return 'Unknown';
  return role.charAt(0).toUpperCase() + role.slice(1);
};

const getRoleClass = (role) => {
  switch (role?.toLowerCase()) {
    case 'owner': return 'role-owner';
    case 'manager': return 'role-manager';
    case 'employee': return 'role-employee';
    default: return 'role-unknown';
  }
};

const getRoleIcon = (role) => {
  switch (role?.toLowerCase()) {
    case 'owner': return 'fa-crown';
    case 'manager': return 'fa-user-tie';
    case 'employee': return 'fa-user';
    default: return 'fa-question';
  }
};

const getStatusClass = (isApproved) => {
  return isApproved ? 'status-active' : 'status-pending';
};

const getStatusIcon = (isApproved) => {
  return isApproved ? 'fa-check-circle' : 'fa-clock';
};

const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString('uk-UA');
};

const formatTime = (dateString) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleTimeString('uk-UA', { 
    hour: '2-digit', 
    minute: '2-digit' 
  });
};

const editUserRole = (user) => {
  // TODO: Implement role editing
  console.log('Edit role for user:', user);
};

const removeUser = (user) => {
  if (confirm(`Are you sure you want to remove ${user.username} from this company?`)) {
    // TODO: Implement user removal
    console.log('Remove user:', user);
  }
};

// Initialize
onMounted(() => {
  fetchUsers();
});
</script>

<style scoped>
.loading-state,
.error-state,
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem;
  color: var(--admin-text-muted);
  font-size: 0.9rem;
}

.error-state {
  color: var(--admin-danger);
}

.users-table-wrapper {
  overflow-x: auto;
  margin-bottom: 1rem;
}

.users-table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th,
.users-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--admin-border-light);
}

.users-table th {
  background: var(--admin-bg-secondary);
  font-weight: 600;
  color: var(--admin-text-secondary);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.user-row:hover {
  background: var(--admin-bg-tertiary);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--admin-bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--admin-text-muted);
  font-size: 0.875rem;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.user-name {
  font-weight: 500;
  color: var(--admin-text-primary);
  font-size: 0.9rem;
}

.user-email {
  font-size: 0.8rem;
  color: var(--admin-text-muted);
}

.role-badge,
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.role-owner {
  background: var(--admin-warning);
  color: var(--admin-text-primary);
}

.role-manager {
  background: var(--admin-info);
  color: white;
}

.role-employee {
  background: var(--admin-primary);
  color: white;
}

.role-unknown {
  background: var(--admin-bg-tertiary);
  color: var(--admin-text-muted);
}

.status-active {
  background: var(--admin-success);
  color: white;
}

.status-pending {
  background: var(--admin-warning);
  color: var(--admin-text-primary);
}

.date-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.date-primary {
  font-size: 0.85rem;
  color: var(--admin-text-primary);
}

.date-secondary {
  font-size: 0.75rem;
  color: var(--admin-text-muted);
}

.action-buttons {
  display: flex;
  gap: 0.25rem;
}

.users-summary {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: var(--admin-bg-secondary);
  border-radius: 6px;
  border: 1px solid var(--admin-border-light);
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.summary-label {
  font-size: 0.75rem;
  color: var(--admin-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.summary-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--admin-text-primary);
}

@media (max-width: 768px) {
  .users-table th,
  .users-table td {
    padding: 0.5rem;
  }
  
  .user-info {
    gap: 0.5rem;
  }
  
  .user-avatar {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }
  
  .users-summary {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
