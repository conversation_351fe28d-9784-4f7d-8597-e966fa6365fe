<template>
  <div class="admin-page">
    <!-- Page Header -->
    <div class="admin-page-header">
      <div class="admin-page-title-section">
        <h1 class="admin-page-title">
          <i class="fas fa-shopping-cart admin-page-icon"></i>
          Orders Management
        </h1>
        <p class="admin-page-subtitle">Manage customer orders, track status, and process payments</p>
      </div>
      <div class="admin-page-actions">
        <button
          class="admin-btn admin-btn-primary"
          @click="exportOrders"
          :disabled="exporting">
          <i class="fas fa-download" :class="{ 'fa-spinner fa-pulse': exporting }"></i>
          Export Orders
        </button>
      </div>
    </div>

    <!-- Search and Filters -->
    <SearchAndFilters
      :filters="filters"
      :filter-fields="filterFields"
      search-label="Search Orders"
      search-placeholder="Order ID, customer name, email..."
      search-column-class="is-4"
      :total-items="totalItems"
      item-name="orders"
      :loading="loading"
      @search-changed="handleSearchChange"
      @filter-changed="handleFilterChange"
      @reset-filters="handleResetFilters"
    />

    <!-- Error State -->
    <div v-if="error" class="admin-error-state">
      <div class="admin-error-icon">
        <i class="fas fa-exclamation-triangle"></i>
      </div>
      <h3 class="admin-error-title">Error Loading Orders</h3>
      <p class="admin-error-message">{{ error }}</p>
      <button @click="forceRefresh" class="admin-btn admin-btn-primary">
        <i class="fas fa-refresh"></i>
        Try Again
      </button>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="admin-loading-state">
      <div class="admin-spinner">
        <i class="fas fa-spinner fa-pulse"></i>
      </div>
      <p class="admin-loading-text">{{ isFirstLoad ? 'Loading orders...' : 'Updating orders...' }}</p>
    </div>

    <!-- Empty State -->
    <div v-else-if="!items || !items.length" class="admin-empty-state">
      <div class="admin-empty-icon">
        <i class="fas fa-shopping-cart"></i>
      </div>
      <h3 class="admin-empty-title">No Orders Found</h3>
      <p class="admin-empty-message">Try adjusting your search criteria or filters</p>
    </div>

    <!-- Orders Table -->
    <div v-else class="admin-card">
      <div class="admin-card-header">
        <h3 class="admin-card-title">
          <i class="fas fa-table"></i>
          Orders List
        </h3>
        <div class="admin-card-actions">
          <span class="admin-results-count">{{ totalItems }} orders found</span>
        </div>
      </div>
      <div class="admin-card-content">
        <div class="admin-table-container">
          <table class="admin-table">
            <thead class="admin-table-header">
              <tr>
                <th class="admin-table-th">Order ID</th>
                <th class="admin-table-th">Customer</th>
                <th class="admin-table-th">Date</th>
                <th class="admin-table-th">Total</th>
                <th class="admin-table-th">Status</th>
                <th class="admin-table-th">Payment</th>
                <th class="admin-table-th admin-table-th-actions">Actions</th>
              </tr>
            </thead>
            <tbody class="admin-table-body">
              <tr v-for="order in items" :key="order.id" class="admin-table-row">
                <td class="admin-table-td">
                  <div class="admin-order-id">{{ order.id }}</div>
                </td>
                <td class="admin-table-td">
                  <div class="admin-customer-info">
                    <div class="admin-customer-name">{{ order.customerName || order.userName || 'N/A' }}</div>
                    <div class="admin-customer-email">{{ order.customerEmail || 'No email' }}</div>
                  </div>
                </td>
                <td class="admin-table-td">
                  <div class="admin-date">{{ formatDate(order.createdAt) }}</div>
                </td>
                <td class="admin-table-td">
                  <div class="admin-price">{{ formatCurrency(order.totalPriceAmount, order.totalPriceCurrency) }}</div>
                </td>
                <td class="admin-table-td">
                  <span class="admin-badge" :class="getOrderStatusClass(order.status)">
                    {{ getOrderStatusText(order.status) }}
                  </span>
                </td>
                <td class="admin-table-td">
                  <span class="admin-badge" :class="getPaymentStatusClass(order.paymentStatusText || order.paymentStatus)">
                    {{ getPaymentStatusText(order.paymentStatusText || order.paymentStatus) }}
                  </span>
                </td>
                <td class="admin-table-td admin-table-actions">
                  <div class="admin-action-buttons">
                    <router-link
                      :to="`/admin/orders/${order.id}/view`"
                      class="admin-btn admin-btn-sm admin-btn-info"
                      title="View Order">
                      <i class="fas fa-eye"></i>
                    </router-link>
                    <router-link
                      :to="`/admin/orders/${order.id}/edit`"
                      class="admin-btn admin-btn-sm admin-btn-primary"
                      title="Edit Order">
                      <i class="fas fa-edit"></i>
                    </router-link>
                    <button
                      class="admin-btn admin-btn-sm admin-btn-warning"
                      @click="openStatusModal(order)"
                      title="Update Status">
                      <i class="fas fa-cog"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Pagination -->
      <div class="admin-card-footer">
        <pagination
          :current-page="currentPage"
          :total-pages="totalPages"
          @page-changed="handlePageChange" />
      </div>
    </div>

    <!-- Status Update Modal -->
    <div v-if="showStatusModal" class="admin-modal">
      <div class="admin-modal-backdrop" @click="closeStatusModal"></div>
      <div class="admin-modal-content">
        <div class="admin-modal-header">
          <h3 class="admin-modal-title">
            <i class="fas fa-edit"></i>
            Update Order Status
          </h3>
          <button class="admin-modal-close" @click="closeStatusModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="admin-modal-body">
          <div v-if="selectedOrder" class="admin-form-grid">
            <div class="admin-form-field admin-form-field-full">
              <div class="admin-order-summary">
                <div class="admin-summary-item">
                  <span class="admin-summary-label">Order ID:</span>
                  <span class="admin-summary-value">{{ selectedOrder.id }}</span>
                </div>
                <div class="admin-summary-item">
                  <span class="admin-summary-label">Customer:</span>
                  <span class="admin-summary-value">{{ selectedOrder.userName }}</span>
                </div>
                <div class="admin-summary-item">
                  <span class="admin-summary-label">Current Status:</span>
                  <span class="admin-badge" :class="getOrderStatusClass(selectedOrder.status)">
                    {{ getOrderStatusText(selectedOrder.status) }}
                  </span>
                </div>
              </div>
            </div>

            <div class="admin-form-field">
              <label class="admin-form-label">New Order Status</label>
              <div class="admin-form-control">
                <select v-model="newStatus" class="admin-form-select">
                  <option value="pending">Pending</option>
                  <option value="processing">Processing</option>
                  <option value="shipped">Shipped</option>
                  <option value="delivered">Delivered</option>
                  <option value="cancelled">Cancelled</option>
                  <option value="refunded">Refunded</option>
                </select>
              </div>
            </div>

            <div class="admin-form-field">
              <label class="admin-form-label">Payment Status</label>
              <div class="admin-form-control">
                <select v-model="newPaymentStatus" class="admin-form-select">
                  <option value="pending">Pending</option>
                  <option value="paid">Paid</option>
                  <option value="failed">Failed</option>
                  <option value="refunded">Refunded</option>
                </select>
              </div>
            </div>

            <div class="admin-form-field admin-form-field-full">
              <label class="admin-form-label">Note (Optional)</label>
              <div class="admin-form-control">
                <textarea
                  class="admin-form-textarea"
                  v-model="statusNote"
                  placeholder="Add a note about this status change"
                  rows="3">
                </textarea>
              </div>
            </div>
          </div>
        </div>
        <div class="admin-modal-footer">
          <button
            class="admin-btn admin-btn-primary"
            @click="updateOrderStatus"
            :disabled="updatingStatus">
            <i v-if="updatingStatus" class="fas fa-spinner fa-pulse"></i>
            <i v-else class="fas fa-save"></i>
            Update Status
          </button>
          <button class="admin-btn admin-btn-secondary" @click="closeStatusModal">
            <i class="fas fa-times"></i>
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, onActivated } from 'vue';
import { ordersService } from '@/admin/services/orders';
import { exportService } from '@/admin/services/exportService';
import { useAdminSearch } from '@/composables/useAdminSearch';
import SearchAndFilters from '@/admin/components/common/SearchAndFilters.vue';
import StatusBadge from '@/admin/components/common/StatusBadge.vue';
import Pagination from '@/admin/components/common/Pagination.vue';
import { eventBus, EVENTS, updateFlags } from '@/admin/utils/eventBus';
import {
  ORDER_STATUS_OPTIONS,
  PAYMENT_STATUS_OPTIONS,
  getOrderStatusText,
  getOrderStatusClass,
  getPaymentStatusText,
  getPaymentStatusClass,
  getCurrencyLocale
} from '@/admin/utils/orderConstants';

// Action loading state
const exporting = ref(false);
const showStatusModal = ref(false);
const selectedOrder = ref(null);
const newStatus = ref('');
const newPaymentStatus = ref('');
const statusNote = ref('');
const updatingStatus = ref(false);

// Filter fields configuration (matching backend enums exactly)
const filterFields = [
  {
    key: 'status',
    label: 'Order Status',
    type: 'select',
    options: [
      { value: '', label: 'All Order Status' },
      { value: 0, label: 'Processing' },  // OrderStatus.Processing = 0
      { value: 1, label: 'Pending' },     // OrderStatus.Pending = 1
      { value: 2, label: 'Shipped' },     // OrderStatus.Shipped = 2
      { value: 3, label: 'Delivered' },   // OrderStatus.Delivered = 3
      { value: 4, label: 'Cancelled' }    // OrderStatus.Cancelled = 4
    ]
  },
  {
    key: 'paymentStatus',
    label: 'Payment Status',
    type: 'select',
    options: [
      { value: '', label: 'All Payment Status' },
      { value: 0, label: 'Pending' },     // PaymentStatus.Pending = 0
      { value: 1, label: 'Completed' },   // PaymentStatus.Completed = 1
      { value: 2, label: 'Refunded' },    // PaymentStatus.Refunded = 2
      { value: 3, label: 'Failed' }       // PaymentStatus.Failed = 3
    ]
  },
  {
    key: 'dateRange',
    label: 'Date Range',
    type: 'select',
    options: [
      { value: '', label: 'All Time' },
      { value: 'today', label: 'Today' },
      { value: 'yesterday', label: 'Yesterday' },
      { value: 'last7days', label: 'Last 7 Days' },
      { value: 'last30days', label: 'Last 30 Days' },
      { value: 'thisMonth', label: 'This Month' },
      { value: 'lastMonth', label: 'Last Month' }
    ]
  }
];

// Use the admin search composable
const {
  items,
  loading,
  error,
  isFirstLoad,
  currentPage,
  totalPages,
  totalItems,
  filters,
  fetchData,
  handlePageChange
} = useAdminSearch({
  fetchFunction: async (params) => {
    try {
      error.value = null;
      const result = await ordersService.getAllAdmin(params);
      isFirstLoad.value = false;
      return result;
    } catch (err) {
      console.error('❌ Error fetching orders:', err);
      error.value = err.message || 'Failed to load orders';
      isFirstLoad.value = false;
      throw err;
    }
  },
  defaultFilters: {
    status: '',
    paymentStatus: '',
    dateRange: ''
  },
  debounceTime: 300,
  defaultPageSize: 10,
  clientSideSearch: false
});



// Event handlers
const handleSearchChange = (searchValue) => {
  console.log('Search changed to:', searchValue);
  filters.search = searchValue;
};

const handleFilterChange = (filterKey, filterValue) => {
  console.log(`Filter ${filterKey} changed to:`, filterValue);
  filters[filterKey] = filterValue;
};

const handleResetFilters = () => {
  console.log('Resetting filters');
  Object.keys(filters).forEach(key => {
    if (key === 'search') {
      filters[key] = '';
    } else {
      filters[key] = '';
    }
  });
  // Trigger data fetch after reset
  fetchData(1);
};

// Export orders
const exportOrders = async () => {
  try {
    exporting.value = true;

    // Prepare export filters using current filters
    const exportFilters = { ...filters };

    // Remove empty filters
    Object.keys(exportFilters).forEach(key => {
      if (!exportFilters[key]) {
        delete exportFilters[key];
      }
    });

    const result = await exportService.exportOrdersToExcel(exportFilters, 'orders_export');

    // Show success notification
    console.log('Export successful:', result);
    alert(`Successfully exported orders to ${result.filename}`);

  } catch (error) {
    console.error('Export failed:', error);
    alert(`Export failed: ${error.message}`);
  } finally {
    exporting.value = false;
  }
};

// Force refresh function
const forceRefresh = async () => {
  try {
    error.value = null;
    ordersService.forceRefresh(); // Clear cache
    await fetchData(1); // Reset to first page
    console.log('🔄 Orders force refreshed successfully');
  } catch (err) {
    console.error('❌ Error during force refresh:', err);
    error.value = err.message || 'Failed to refresh orders';
  }
};

// Format date
const formatDate = (dateString) => {
  if (!dateString) return '';
  
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(dateString));
};

// Format currency with support for different currencies using standardized constants
const formatCurrency = (value, currency = 'UAH') => {
  if (!value) return '₴0.00';

  // Округлюємо до 2 знаків після коми
  const numValue = typeof value === 'string' ? parseFloat(value) : value;

  // Use standardized currency locale mapping
  const currencyInfo = getCurrencyLocale(currency);

  return new Intl.NumberFormat(currencyInfo.locale, {
    style: 'currency',
    currency: currencyInfo.currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(numValue);
};

// Status utility functions are now imported from orderConstants





// Open status modal
const openStatusModal = (order) => {
  console.log('Opening status modal for order:', order);
  selectedOrder.value = order;
  newStatus.value = order.status;
  // Use numeric payment status, not text
  newPaymentStatus.value = order.paymentStatus !== undefined ? order.paymentStatus : 0;
  statusNote.value = '';
  showStatusModal.value = true;
  console.log('Modal state:', {
    orderId: order.id,
    currentStatus: order.status,
    currentPaymentStatus: order.paymentStatus,
    newStatus: newStatus.value,
    newPaymentStatus: newPaymentStatus.value
  });
};

// Close status modal
const closeStatusModal = () => {
  showStatusModal.value = false;
  selectedOrder.value = null;
};

// Update order status
const updateOrderStatus = async () => {
  if (!selectedOrder.value) return;

  updatingStatus.value = true;

  try {
    console.log('Updating order status:', {
      orderId: selectedOrder.value.id,
      oldStatus: selectedOrder.value.status,
      newStatus: newStatus.value,
      oldPaymentStatus: selectedOrder.value.paymentStatus,
      newPaymentStatus: newPaymentStatus.value
    });

    // Update order status
    if (newStatus.value !== selectedOrder.value.status) {
      await ordersService.updateOrderStatus(selectedOrder.value.id, newStatus.value);
      console.log('✅ Order status updated successfully');
    }

    // Update payment status
    if (newPaymentStatus.value !== selectedOrder.value.paymentStatus) {
      await ordersService.updatePaymentStatus(selectedOrder.value.id, newPaymentStatus.value);
      console.log('✅ Payment status updated successfully');
    }

    // Add note if provided
    if (statusNote.value.trim()) {
      await ordersService.addOrderNote(selectedOrder.value.id, statusNote.value);
      console.log('✅ Order note added successfully');
    }

    // Refresh the entire list to get updated data including totals
    await fetchData(currentPage.value);
    console.log('✅ Order list refreshed after status update');

    // Close modal
    closeStatusModal();
    console.log('✅ Status update completed successfully');
  } catch (error) {
    console.error('❌ Error updating order status:', error);
    // Show user-friendly error message
    alert('Failed to update order status. Please try again.');
  } finally {
    updatingStatus.value = false;
  }
};





// Event listeners
let unsubscribeOrderUpdated;
let windowFocusHandler;

// Lifecycle hooks
onMounted(() => {
  // Check if orders need to be refreshed due to changes in other components
  if (updateFlags.shouldRefreshOrders()) {
    console.log('🔄 Orders marked for refresh, loading fresh data...');
    updateFlags.clearOrdersRefreshFlag();
  }

  fetchData();

  // Listen for order updates from other components
  unsubscribeOrderUpdated = eventBus.on(EVENTS.ORDER_UPDATED, () => {
    console.log('📡 Received order update event, refreshing list...');
    fetchData(currentPage.value);
  });

  // Listen for window focus events (when user returns to the tab)
  windowFocusHandler = () => {
    if (updateFlags.shouldRefreshOrders()) {
      console.log('🔄 Window focused and orders need refresh, loading fresh data...');
      updateFlags.clearOrdersRefreshFlag();
      fetchData(currentPage.value);
    }
  };
  window.addEventListener('focus', windowFocusHandler);
});

onUnmounted(() => {
  // Clean up event listeners
  if (unsubscribeOrderUpdated) {
    unsubscribeOrderUpdated();
  }
  if (windowFocusHandler) {
    window.removeEventListener('focus', windowFocusHandler);
  }
});

// This hook is called every time the component becomes active
// (including when navigating back from other pages)
onActivated(() => {
  console.log('📍 OrderList component activated');

  // Check if orders need to be refreshed
  if (updateFlags.shouldRefreshOrders()) {
    console.log('🔄 Orders marked for refresh, loading fresh data...');
    updateFlags.clearOrdersRefreshFlag();
    fetchData(currentPage.value);
  }
});
</script>

<style scoped>
/* Order specific styling */
.admin-order-id {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: var(--admin-primary-color);
  background: var(--admin-bg-light);
  padding: 4px 8px;
  border-radius: var(--admin-border-radius-sm);
  font-size: 13px;
}

.admin-customer-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.admin-customer-name {
  font-weight: 600;
  color: var(--admin-text-primary);
}

.admin-customer-email {
  font-size: 12px;
  color: var(--admin-text-secondary);
}

.admin-price {
  font-weight: 600;
  color: var(--admin-success-color);
  font-size: 14px;
}

.admin-date {
  color: var(--admin-text-secondary);
  font-size: 14px;
}

/* Order summary in modal */
.admin-order-summary {
  background: var(--admin-bg-light);
  border-radius: var(--admin-border-radius);
  padding: var(--admin-spacing-md);
  margin-bottom: var(--admin-spacing-md);
}

.admin-summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--admin-border-color);
}

.admin-summary-item:last-child {
  border-bottom: none;
}

.admin-summary-label {
  font-weight: 600;
  color: var(--admin-text-secondary);
}

.admin-summary-value {
  color: var(--admin-text-primary);
  font-weight: 500;
}

/* Badge styling for order statuses */
.admin-badge.is-pending {
  background: #ffc107;
  color: #000;
}

.admin-badge.is-processing {
  background: #17a2b8;
  color: white;
}

.admin-badge.is-shipped {
  background: #007bff;
  color: white;
}

.admin-badge.is-delivered {
  background: #28a745;
  color: white;
}

.admin-badge.is-cancelled {
  background: #dc3545;
  color: white;
}

.admin-badge.is-refunded {
  background: #6c757d;
  color: white;
}

.admin-badge.is-paid {
  background: #28a745;
  color: white;
}

.admin-badge.is-failed {
  background: #dc3545;
  color: white;
}
</style>
