<template>
  <div class="admin-card">
    <div class="admin-card-header">
      <h3 class="admin-card-title">
        <i class="fas fa-clock admin-card-icon"></i>
        Working Schedule
      </h3>
    </div>
    <div class="admin-card-content">
      
      <div class="schedule-table-wrapper">
        <table class="admin-table schedule-table">
          <thead>
            <tr>
              <th class="day-column">Day</th>
              <th class="status-column">Status</th>
              <th class="time-column">Working Hours</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="day in weekDays" :key="day.value" class="schedule-row">
              <td class="day-cell">
                <div class="day-info">
                  <span class="day-name">{{ day.name }}</span>
                  <span class="day-short">{{ day.short }}</span>
                </div>
              </td>
              <td class="status-cell">
                <span 
                  class="status-badge" 
                  :class="getStatusClass(day.value)">
                  <i class="fas" :class="getStatusIcon(day.value)"></i>
                  {{ getStatusText(day.value) }}
                </span>
              </td>
              <td class="time-cell">
                <div v-if="!isDayClosed(day.value)" class="time-range">
                  <span class="time-start">{{ formatTime(getOpenTime(day.value)) }}</span>
                  <span class="time-separator">—</span>
                  <span class="time-end">{{ formatTime(getCloseTime(day.value)) }}</span>
                </div>
                <span v-else class="closed-text">Closed</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Current Status -->
      <div class="current-status">
        <div class="current-status-header">
          <i class="fas fa-info-circle"></i>
          <span>Current Status</span>
        </div>
        <div class="current-status-content">
          <span class="status-badge" :class="currentStatusClass">
            <i class="fas" :class="currentStatusIcon"></i>
            {{ currentStatusText }}
          </span>
          <span v-if="nextChangeText" class="next-change">
            {{ nextChangeText }}
          </span>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  schedule: {
    type: Array,
    required: true,
    default: () => []
  }
});

const weekDays = [
  { value: 0, name: 'Monday', short: 'Mon' },
  { value: 1, name: 'Tuesday', short: 'Tue' },
  { value: 2, name: 'Wednesday', short: 'Wed' },
  { value: 3, name: 'Thursday', short: 'Thu' },
  { value: 4, name: 'Friday', short: 'Fri' },
  { value: 5, name: 'Saturday', short: 'Sat' },
  { value: 6, name: 'Sunday', short: 'Sun' }
];

const scheduleMap = computed(() => {
  const map = {};
  props.schedule.forEach(item => {
    map[item.day] = item;
  });
  return map;
});

const isDayClosed = (dayValue) => {
  const daySchedule = scheduleMap.value[dayValue];
  return !daySchedule || daySchedule.isClosed;
};

const getOpenTime = (dayValue) => {
  const daySchedule = scheduleMap.value[dayValue];
  return daySchedule?.openTime || '00:00:00';
};

const getCloseTime = (dayValue) => {
  const daySchedule = scheduleMap.value[dayValue];
  return daySchedule?.closeTime || '00:00:00';
};

const getStatusClass = (dayValue) => {
  return isDayClosed(dayValue) ? 'status-closed' : 'status-open';
};

const getStatusIcon = (dayValue) => {
  return isDayClosed(dayValue) ? 'fa-times-circle' : 'fa-check-circle';
};

const getStatusText = (dayValue) => {
  return isDayClosed(dayValue) ? 'Closed' : 'Open';
};

const formatTime = (timeString) => {
  if (!timeString) return '--:--';
  
  // Handle TimeSpan format (HH:mm:ss)
  const parts = timeString.split(':');
  if (parts.length >= 2) {
    const hours = parseInt(parts[0]);
    const minutes = parts[1];
    return `${hours.toString().padStart(2, '0')}:${minutes}`;
  }
  
  return timeString;
};

// Current status logic
const currentStatus = computed(() => {
  const now = new Date();
  const currentDay = (now.getDay() + 6) % 7; // Convert Sunday=0 to Monday=0
  const currentTime = now.getHours() * 60 + now.getMinutes();
  
  const todaySchedule = scheduleMap.value[currentDay];
  
  if (!todaySchedule || todaySchedule.isClosed) {
    return { isOpen: false, reason: 'closed_today' };
  }
  
  const openTime = parseTime(todaySchedule.openTime);
  const closeTime = parseTime(todaySchedule.closeTime);
  
  if (currentTime >= openTime && currentTime < closeTime) {
    return { isOpen: true, closeTime: closeTime };
  } else if (currentTime < openTime) {
    return { isOpen: false, reason: 'not_open_yet', openTime: openTime };
  } else {
    return { isOpen: false, reason: 'closed_for_day' };
  }
});

const parseTime = (timeString) => {
  if (!timeString) return 0;
  const parts = timeString.split(':');
  return parseInt(parts[0]) * 60 + parseInt(parts[1]);
};

const formatMinutesToTime = (minutes) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
};

const currentStatusClass = computed(() => {
  return currentStatus.value.isOpen ? 'status-open' : 'status-closed';
});

const currentStatusIcon = computed(() => {
  return currentStatus.value.isOpen ? 'fa-check-circle' : 'fa-times-circle';
});

const currentStatusText = computed(() => {
  if (currentStatus.value.isOpen) {
    return 'Currently Open';
  }
  
  switch (currentStatus.value.reason) {
    case 'closed_today':
      return 'Closed Today';
    case 'not_open_yet':
      return 'Opens Later Today';
    case 'closed_for_day':
      return 'Closed for Today';
    default:
      return 'Closed';
  }
});

const nextChangeText = computed(() => {
  if (currentStatus.value.isOpen && currentStatus.value.closeTime) {
    return `Closes at ${formatMinutesToTime(currentStatus.value.closeTime)}`;
  }
  
  if (currentStatus.value.reason === 'not_open_yet' && currentStatus.value.openTime) {
    return `Opens at ${formatMinutesToTime(currentStatus.value.openTime)}`;
  }
  
  return null;
});
</script>

<style scoped>
.schedule-table-wrapper {
  overflow-x: auto;
  margin-bottom: 1.5rem;
}

.schedule-table {
  width: 100%;
  border-collapse: collapse;
}

.schedule-table th,
.schedule-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--admin-border-light);
}

.schedule-table th {
  background: var(--admin-bg-secondary);
  font-weight: 600;
  color: var(--admin-text-secondary);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.day-column {
  width: 30%;
}

.status-column {
  width: 25%;
}

.time-column {
  width: 45%;
}

.schedule-row:hover {
  background: var(--admin-bg-tertiary);
}

.day-cell {
  font-weight: 500;
}

.day-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.day-name {
  font-size: 0.9rem;
  color: var(--admin-text-primary);
}

.day-short {
  font-size: 0.75rem;
  color: var(--admin-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-open {
  background: var(--admin-success);
  color: white;
}

.status-closed {
  background: var(--admin-danger);
  color: white;
}

.time-range {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  font-weight: 600;
}

.time-start,
.time-end {
  color: var(--admin-text-primary);
}

.time-separator {
  color: var(--admin-text-muted);
}

.closed-text {
  color: var(--admin-text-muted);
  font-style: italic;
}

.current-status {
  background: var(--admin-bg-secondary);
  border: 1px solid var(--admin-border-light);
  border-radius: 8px;
  padding: 1rem;
}

.current-status-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--admin-text-secondary);
}

.current-status-header i {
  color: var(--admin-text-muted);
}

.current-status-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.next-change {
  font-size: 0.85rem;
  color: var(--admin-text-muted);
}

@media (max-width: 768px) {
  .schedule-table th,
  .schedule-table td {
    padding: 0.5rem;
  }
  
  .day-info {
    gap: 0.125rem;
  }
  
  .day-name {
    font-size: 0.85rem;
  }
  
  .day-short {
    font-size: 0.7rem;
  }
  
  .time-range {
    font-size: 0.8rem;
  }
}
</style>
