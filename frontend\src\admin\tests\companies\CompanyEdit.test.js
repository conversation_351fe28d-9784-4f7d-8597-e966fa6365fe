import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { createRouter, createWebHistory } from 'vue-router';
import CompanyEdit from '@/admin/views/companies/CompanyEdit.vue';
import { companiesService } from '@/admin/services/companies';

// Mock the companies service
vi.mock('@/admin/services/companies', () => ({
  companiesService: {
    getDetailedCompany: vi.fn(),
    updateDetailedCompany: vi.fn()
  }
}));

describe('CompanyEdit', () => {
  let wrapper;
  let router;

  const mockCompany = {
    id: '123',
    name: 'Test Company',
    slug: 'test-company',
    description: 'Test Description',
    contactEmail: '<EMAIL>',
    contactPhone: '+************',
    addressRegion: 'Kyiv Oblast',
    addressCity: 'Kyiv',
    addressStreet: '123 Main Street',
    addressPostalCode: '01001',
    imageUrl: 'https://example.com/image.jpg',
    isFeatured: false,
    finance: {
      bankName: 'Test Bank',
      bankAccount: '**********123456',
      bankCode: '123456',
      taxId: '**********',
      paymentDetails: 'Test payment details'
    }
  };

  beforeEach(() => {
    // Create router
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/admin/companies/:id/edit', component: CompanyEdit },
        { path: '/admin/companies/:id', name: 'CompanyDetail' }
      ]
    });

    // Push to the route
    router.push('/admin/companies/123/edit');

    // Mock the service response
    companiesService.getDetailedCompany.mockResolvedValue({
      data: mockCompany
    });

    companiesService.updateDetailedCompany.mockResolvedValue({
      success: true
    });
  });

  it('renders loading state initially', async () => {
    wrapper = mount(CompanyEdit, {
      global: {
        plugins: [router]
      }
    });

    expect(wrapper.find('.admin-loading-state').exists()).toBe(true);
  });

  it('renders form fields after loading', async () => {
    wrapper = mount(CompanyEdit, {
      global: {
        plugins: [router]
      }
    });

    // Wait for the component to load
    await wrapper.vm.$nextTick();
    await new Promise(resolve => setTimeout(resolve, 100));

    // Check basic information fields
    expect(wrapper.find('input[placeholder="Enter company name"]').exists()).toBe(true);
    expect(wrapper.find('input[placeholder="<EMAIL>"]').exists()).toBe(true);
    expect(wrapper.find('input[placeholder="+380 XX XXX XXXX"]').exists()).toBe(true);
    expect(wrapper.find('textarea[placeholder="Describe the company..."]').exists()).toBe(true);

    // Check address fields
    expect(wrapper.find('input[placeholder="e.g., Kyiv Oblast"]').exists()).toBe(true);
    expect(wrapper.find('input[placeholder="e.g., Kyiv"]').exists()).toBe(true);
    expect(wrapper.find('input[placeholder="e.g., 01001"]').exists()).toBe(true);

    // Check financial fields
    expect(wrapper.find('input[placeholder="e.g., PrivatBank"]').exists()).toBe(true);
    expect(wrapper.find('input[placeholder="e.g., 305299"]').exists()).toBe(true);
    expect(wrapper.find('input[placeholder="e.g., **********"]').exists()).toBe(true);
  });

  it('populates form with company data', async () => {
    wrapper = mount(CompanyEdit, {
      global: {
        plugins: [router]
      }
    });

    await wrapper.vm.$nextTick();
    await new Promise(resolve => setTimeout(resolve, 100));

    // Check if form is populated with mock data
    const nameInput = wrapper.find('input[placeholder="Enter company name"]');
    const emailInput = wrapper.find('input[placeholder="<EMAIL>"]');
    
    expect(nameInput.element.value).toBe('Test Company');
    expect(emailInput.element.value).toBe('<EMAIL>');
  });

  it('validates required fields', async () => {
    wrapper = mount(CompanyEdit, {
      global: {
        plugins: [router]
      }
    });

    await wrapper.vm.$nextTick();
    await new Promise(resolve => setTimeout(resolve, 100));

    // Clear required field
    const nameInput = wrapper.find('input[placeholder="Enter company name"]');
    await nameInput.setValue('');

    // Try to submit form
    const form = wrapper.find('form');
    await form.trigger('submit.prevent');

    // Check if validation prevents submission
    expect(companiesService.updateDetailedCompany).not.toHaveBeenCalled();
  });

  it('submits form with correct data', async () => {
    wrapper = mount(CompanyEdit, {
      global: {
        plugins: [router]
      }
    });

    await wrapper.vm.$nextTick();
    await new Promise(resolve => setTimeout(resolve, 100));

    // Fill form with test data
    const nameInput = wrapper.find('input[placeholder="Enter company name"]');
    await nameInput.setValue('Updated Company Name');

    // Submit form
    const form = wrapper.find('form');
    await form.trigger('submit.prevent');

    await wrapper.vm.$nextTick();

    // Check if service was called with correct data
    expect(companiesService.updateDetailedCompany).toHaveBeenCalledWith(
      '123',
      expect.objectContaining({
        company: expect.objectContaining({
          name: 'Updated Company Name'
        })
      })
    );
  });

  it('handles form reset correctly', async () => {
    wrapper = mount(CompanyEdit, {
      global: {
        plugins: [router]
      }
    });

    await wrapper.vm.$nextTick();
    await new Promise(resolve => setTimeout(resolve, 100));

    // Change form data
    const nameInput = wrapper.find('input[placeholder="Enter company name"]');
    await nameInput.setValue('Changed Name');

    // Reset form
    const resetButton = wrapper.find('button:contains("Reset")');
    await resetButton.trigger('click');

    // Check if form is reset to original values
    expect(nameInput.element.value).toBe('Test Company');
  });

  it('shows error state when loading fails', async () => {
    companiesService.getDetailedCompany.mockRejectedValue(new Error('Failed to load'));

    wrapper = mount(CompanyEdit, {
      global: {
        plugins: [router]
      }
    });

    await wrapper.vm.$nextTick();
    await new Promise(resolve => setTimeout(resolve, 100));

    expect(wrapper.find('.admin-alert-danger').exists()).toBe(true);
  });

  it('disables form during saving', async () => {
    wrapper = mount(CompanyEdit, {
      global: {
        plugins: [router]
      }
    });

    await wrapper.vm.$nextTick();
    await new Promise(resolve => setTimeout(resolve, 100));

    // Mock a slow save operation
    companiesService.updateDetailedCompany.mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 1000))
    );

    // Submit form
    const form = wrapper.find('form');
    await form.trigger('submit.prevent');

    // Check if save button is disabled
    const saveButton = wrapper.find('button[type="submit"]');
    expect(saveButton.attributes('disabled')).toBeDefined();
  });
});
