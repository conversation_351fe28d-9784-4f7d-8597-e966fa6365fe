<template>
  <error-boundary>
    <div class="category-list">
      <div class="level">
        <div class="level-left">
          <div class="level-item">
            <h1 class="title">Categories</h1>
          </div>
        </div>
        <div class="level-right">
          <div class="level-item">
            <router-link to="/admin/categories/create" class="button is-primary">
              <span class="icon">
                <i class="fas fa-plus"></i>
              </span>
              <span>Add Category</span>
            </router-link>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="card mb-4">
        <div class="card-content">
          <div class="columns">
            <div class="column is-4">
              <div class="field">
                <label class="label">Search</label>
                <div class="control has-icons-left">
                  <input
                    class="input"
                    type="text"
                    placeholder="Search categories..."
                    v-model="filters.search"
                    @input="debouncedSearch">
                  <span class="icon is-small is-left">
                    <i class="fas fa-search"></i>
                  </span>
                </div>
              </div>
            </div>
            <div class="column is-4">
              <div class="field">
                <label class="label">Status</label>
                <div class="control">
                  <div class="select is-fullwidth">
                    <select v-model="filters.status" @change="fetchCategories">
                      <option value="">All Statuses</option>
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div class="column is-4">
              <div class="field">
                <label class="label">&nbsp;</label>
                <div class="field is-grouped">
                  <div class="control">
                    <button
                      class="button is-light"
                      @click="resetFilters">
                      Reset
                    </button>
                  </div>
                  <div class="control">
                    <button
                      class="button is-primary"
                      @click="fetchCategories"
                      :class="{ 'is-loading': loading }">
                      Apply Filters
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>



      <!-- Categories Content -->
      <div class="card">
        <div class="card-content">
          <!-- Loading Skeleton -->
          <category-skeleton
            v-if="loading && !categories.length"
            :view-mode="viewMode"
            :count="5" />

          <!-- Error State -->
          <div v-else-if="error" class="has-text-centered py-6">
            <span class="icon is-large has-text-danger">
              <i class="fas fa-exclamation-triangle fa-2x"></i>
            </span>
            <p class="mt-2 has-text-danger has-text-weight-bold">{{ error }}</p>
            <div class="mt-4">
              <button class="button is-primary" @click="fetchCategories">
                <span class="icon">
                  <i class="fas fa-sync-alt"></i>
                </span>
                <span>Try Again</span>
              </button>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else-if="!categories.length" class="has-text-centered py-6">
            <span class="icon is-large">
              <i class="fas fa-folder-open fa-2x"></i>
            </span>
            <p class="mt-2">No categories found</p>
            <p class="mt-2">Create your first category to organize your products</p>
            <div class="mt-4">
              <router-link to="/admin/categories/create" class="button is-primary">
                <span class="icon">
                  <i class="fas fa-plus"></i>
                </span>
                <span>Add Category</span>
              </router-link>
            </div>
          </div>

          <!-- Content -->
          <div v-else>
            <div class="table-container">
              <table class="table is-fullwidth is-hoverable">
                <thead>
                  <tr>
                    <th>Image</th>
                    <th>Name</th>
                    <th>Products</th>
                    <th>Slug</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="category in categories" :key="category.id">
                    <td class="image-cell">
                      <figure class="image is-48x48">
                        <img
                          :src="category.image || 'https://via.placeholder.com/48'"
                          :alt="category.name"
                          @error="handleImageError">
                      </figure>
                    </td>
                    <td>
                      <strong>{{ category.name }}</strong>
                      <br>
                      <small class="has-text-grey">{{ category.description || 'No description' }}</small>
                    </td>
                    <td>
                      <span class="tag is-info">{{ getProductCount(category.id) }}</span>
                    </td>
                    <td>
                      <code>{{ category.slug }}</code>
                    </td>
                    <td>
                      <div class="buttons are-small">
                        <router-link
                          :to="`/admin/categories/${category.id}`"
                          class="button is-info"
                          title="View">
                          <span class="icon is-small">
                            <i class="fas fa-eye"></i>
                          </span>
                        </router-link>
                        <router-link
                          :to="`/admin/categories/${category.id}/edit`"
                          class="button is-primary"
                          title="Edit">
                          <span class="icon is-small">
                            <i class="fas fa-edit"></i>
                          </span>
                        </router-link>
                        <button
                          class="button is-danger"
                          @click="confirmDelete(category)"
                          title="Delete">
                          <span class="icon is-small">
                            <i class="fas fa-trash"></i>
                          </span>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <confirm-dialog
      :is-open="showDeleteModal"
      title="Delete Category"
      :message="`Are you sure you want to delete '${categoryToDelete?.name}'? This may affect products in this category.`"
      confirm-text="Delete"
      cancel-text="Cancel"
      @confirm="deleteCategory"
      @cancel="cancelDelete" />
  </error-boundary>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, inject } from 'vue';
import { categoriesService } from '@/admin/services/categories';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';
import CategorySkeleton from '@/admin/components/categories/CategorySkeleton.vue';
import ErrorBoundary from '@/components/ErrorBoundary.vue';



// Error boundary
const errorBoundary = inject('errorBoundary', {
  setError: () => {},
  clearError: () => {}
});

// State
const categories = ref([]);
const loading = ref(false);
const error = ref(null);
const showDeleteModal = ref(false);
const categoryToDelete = ref(null);
const searchTimeout = ref(null);

// Filters
const filters = reactive({
  search: '',
  status: ''
});

// Debounced search
const debouncedSearch = () => {
  // Clear any existing timeout
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }

  // Set a new timeout
  searchTimeout.value = setTimeout(() => {
    fetchCategories();
    searchTimeout.value = null;
  }, 300); // 300ms debounce
};

// Fetch categories directly from service
const fetchCategories = async () => {
  try {
    // Clear any error
    errorBoundary.clearError();
    error.value = null;

    // Set loading state
    loading.value = true;

    // Fetch categories directly from service
    const response = await categoriesService.getCategories(filters);

    console.log('Categories API response:', response);

    // Handle different response structures
    if (response && response.categories) {
      categories.value = response.categories;
    } else if (response && response.data) {
      categories.value = response.data;
    } else if (Array.isArray(response)) {
      categories.value = response;
    } else {
      categories.value = [];
    }

    console.log('Categories loaded:', categories.value.length);
  } catch (err) {
    console.error('Error fetching categories:', err);

    // Set local error state for component-level error display
    error.value = err.message || 'Failed to load categories. Please try again.';

    // Only set error boundary for non-404 errors (to avoid full-page error)
    if (!err.originalError || err.originalError.response?.status !== 404) {
      errorBoundary.setError(err);
    }

    categories.value = [];
  } finally {
    loading.value = false;
  }
};



// Reset filters
const resetFilters = () => {
  filters.search = '';
  filters.status = '';
  fetchCategories();
};

// Get product count for category
const getProductCount = (categoryId) => {
  // Тут можна додати реальний підрахунок продуктів з API
  // Поки що повертаємо випадкове число для демонстрації
  const category = categories.value.find(c => c.id === categoryId);
  return category?.productCount || Math.floor(Math.random() * 50);
};

// Handle image error
const handleImageError = (event) => {
  event.target.src = 'https://via.placeholder.com/48?text=No+Image';
};



// Confirm delete
const confirmDelete = (category) => {
  categoryToDelete.value = category;
  showDeleteModal.value = true;
};

// Delete category
const deleteCategory = async () => {
  if (!categoryToDelete.value) return;

  try {
    // Clear any error
    errorBoundary.clearError();

    // Delete category using service
    await categoriesService.deleteCategory(categoryToDelete.value.id);

    // Remove from local list
    categories.value = categories.value.filter(c => c.id !== categoryToDelete.value.id);

    showDeleteModal.value = false;
    categoryToDelete.value = null;
  } catch (error) {
    console.error('Error deleting category:', error);
    errorBoundary.setError(error);
  }
};

// Cancel delete
const cancelDelete = () => {
  showDeleteModal.value = false;
  categoryToDelete.value = null;
};



// Lifecycle hooks
onMounted(() => {
  // Fetch initial data
  fetchCategories();
});

// Clean up resources when component is unmounted
onUnmounted(() => {
  // Clear any pending timeouts
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }
});
</script>

<style scoped>
.category-list {
  padding: 1rem;
}

.title {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.py-4 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-6 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.image-cell {
  width: 60px;
}

.table th {
  font-weight: 600;
  color: #363636;
  background-color: #f9f9f5;
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}

.tree-container {
  padding: 1rem;
}

.tree-root {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tree-item {
  margin-bottom: 0.5rem;
}

.tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.tree-node:hover {
  background-color: #f5f5f5;
}

.node-content {
  display: flex;
  align-items: center;
}

.node-name {
  font-weight: 500;
  margin-right: 0.5rem;
}

.node-count {
  color: #7a7a7a;
  font-size: 0.9rem;
}

.node-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.tree-node:hover .node-actions {
  opacity: 1;
}
</style>
