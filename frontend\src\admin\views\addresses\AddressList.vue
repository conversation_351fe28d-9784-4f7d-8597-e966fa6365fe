<template>
  <div class="address-list">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Address Management</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <div class="buttons">
            <button
              class="button is-primary"
              @click="showCreateModal = true"
            >
              <span class="icon">
                <i class="fas fa-plus"></i>
              </span>
              <span>Add Address</span>
            </button>
            <button
              class="button is-danger"
              @click="bulkDelete"
              :disabled="selectedAddresses.length === 0 || actionLoading"
              v-if="selectedAddresses.length > 0"
            >
              <span class="icon">
                <i class="fas fa-trash"></i>
              </span>
              <span>Delete Selected ({{ selectedAddresses.length }})</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <SearchAndFilters
      v-model:search="searchQuery"
      :filters="filters"
      :filter-fields="filterFields"
      search-label="Search Addresses"
      search-placeholder="Search by region, city, street, or user name..."
      search-column-class="is-4"
      :total-items="totalItems"
      item-name="addresses"
      :loading="loading"
      @search-changed="handleSearchChange"
      @filter-changed="handleFilterChange"
      @reset-filters="handleResetFilters"
    />

    <!-- Loading -->
    <div class="has-text-centered py-6" v-if="loading && isFirstLoad">
      <span class="icon is-large">
        <i class="fas fa-spinner fa-pulse fa-2x"></i>
      </span>
      <p class="mt-2">Loading addresses...</p>
    </div>

    <!-- Error -->
    <div class="notification is-danger" v-else-if="error">
      <p>{{ error }}</p>
      <button class="button is-light mt-2" @click="fetchAddresses">
        <span class="icon"><i class="fas fa-redo"></i></span>
        <span>Retry</span>
      </button>
    </div>

    <!-- Addresses Table -->
    <div class="card" v-else>
      <div class="card-content">
        <div class="table-container" :class="{ 'is-loading': loading && !isFirstLoad }">
          <table class="table is-fullwidth is-hoverable">
            <thead>
              <tr>
                <th>
                  <label class="checkbox">
                    <input
                      type="checkbox"
                      @change="toggleSelectAll"
                      :checked="allSelected"
                      :indeterminate="someSelected"
                    />
                  </label>
                </th>
                <th>User</th>
                <th>Region</th>
                <th>City</th>
                <th>Street</th>
                <th>Postal Code</th>
                <th>Created</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="address in addresses" :key="address.id">
                <td>
                  <label class="checkbox">
                    <input
                      type="checkbox"
                      :value="address.id"
                      v-model="selectedAddresses"
                    />
                  </label>
                </td>
                <td>
                  <router-link
                    v-if="address.userId"
                    :to="{ name: 'AdminUserDetail', params: { id: address.userId } }"
                    class="has-text-link"
                  >
                    {{ address.userName || 'Unknown User' }}
                  </router-link>
                  <span v-else class="has-text-grey">No User</span>
                </td>
                <td>{{ address.addressVO?.region || address.region || 'N/A' }}</td>
                <td>{{ address.addressVO?.city || address.city || 'N/A' }}</td>
                <td>{{ address.addressVO?.street || address.street || 'N/A' }}</td>
                <td>{{ address.addressVO?.postalCode || address.postalCode || 'N/A' }}</td>
                <td>{{ formatDate(address.createdAt) }}</td>
                <td>
                  <div class="buttons">
                    <button
                      class="button is-small is-info"
                      @click="editAddress(address)"
                      :disabled="actionLoading">
                      <span class="icon">
                        <i class="fas fa-edit"></i>
                      </span>
                      <span>Edit</span>
                    </button>
                    <button
                      class="button is-small is-danger"
                      @click="deleteAddress(address.id)"
                      :disabled="actionLoading">
                      <span class="icon">
                        <i class="fas fa-trash"></i>
                      </span>
                      <span>Delete</span>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <Pagination
      v-if="totalPages > 1"
      :current-page="currentPage"
      :total-pages="totalPages"
      @page-changed="handlePageChange"
    />

    <!-- Create/Edit Address Modal -->
    <div class="modal" :class="{ 'is-active': showCreateModal || showEditModal }">
      <div class="modal-background" @click="closeModal"></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">
            {{ showEditModal ? 'Edit Address' : 'Create New Address' }}
          </p>
          <button class="delete" @click="closeModal"></button>
        </header>
        <section class="modal-card-body">
          <AddressForm
            :address="editingAddress"
            :loading="formLoading"
            @submit="handleFormSubmit"
            @cancel="closeModal"
          />
        </section>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { addressesService } from '@/admin/services/addresses';
import SearchAndFilters from '@/components/admin/SearchAndFilters.vue';
import Pagination from '@/components/admin/Pagination.vue';
import AddressForm from '@/components/admin/AddressForm.vue';

// Utility function for debouncing
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Reactive data
const addresses = ref([]);
const loading = ref(false);
const error = ref(null);
const actionLoading = ref(false);
const formLoading = ref(false);
const searchQuery = ref('');
const selectedAddresses = ref([]);
const isFirstLoad = ref(true);

// Modal states
const showCreateModal = ref(false);
const showEditModal = ref(false);
const editingAddress = ref(null);

// Filters
const filters = ref({
  region: '',
  hasUser: ''
});

const filterFields = ref({
  region: {
    type: 'text',
    label: 'Region',
    placeholder: 'Filter by region...'
  },
  hasUser: {
    type: 'select',
    label: 'User Status',
    options: [
      { value: 'true', label: 'Has User' },
      { value: 'false', label: 'No User' }
    ]
  }
});

const sortBy = ref('createdAt');
const sortOrder = ref('desc');

// Pagination
const currentPage = ref(1);
const totalPages = ref(1);
const totalItems = ref(0);
const pageSize = ref(15);

// Computed
const allSelected = computed(() => {
  return addresses.value.length > 0 && selectedAddresses.value.length === addresses.value.length;
});

const someSelected = computed(() => {
  return selectedAddresses.value.length > 0 && selectedAddresses.value.length < addresses.value.length;
});

// Methods
const fetchAddresses = async () => {
  loading.value = true;
  error.value = null;

  try {
    const params = {
      filter: searchQuery.value,
      region: filters.value.region,
      hasUser: filters.value.hasUser,
      orderBy: sortBy.value,
      descending: sortOrder.value === 'desc',
      page: currentPage.value,
      pageSize: pageSize.value
    };

    const response = await addressesService.getAddresses(params);
    addresses.value = response.data || [];
    currentPage.value = response.currentPage || 1;
    totalPages.value = response.totalPages || 1;
    totalItems.value = response.totalItems || 0;

    // Clear selections when data changes
    selectedAddresses.value = [];
  } catch (err) {
    error.value = err.message || 'Failed to load addresses';
    addresses.value = [];
  } finally {
    loading.value = false;
    isFirstLoad.value = false;
  }
};

const handleSearchChange = (query) => {
  searchQuery.value = query;
  currentPage.value = 1;
  debouncedSearch();
};

const handleFilterChange = () => {
  currentPage.value = 1;
  fetchAddresses();
};

const handleResetFilters = () => {
  searchQuery.value = '';
  filters.value.region = '';
  filters.value.hasUser = '';
  currentPage.value = 1;
  fetchAddresses();
};

const handlePageChange = (page) => {
  currentPage.value = page;
  fetchAddresses();
};

const debouncedSearch = debounce(fetchAddresses, 300);

const toggleSelectAll = () => {
  if (allSelected.value) {
    selectedAddresses.value = [];
  } else {
    selectedAddresses.value = addresses.value.map(address => address.id);
  }
};

const editAddress = (address) => {
  editingAddress.value = address;
  showEditModal.value = true;
};

const closeModal = () => {
  showCreateModal.value = false;
  showEditModal.value = false;
  editingAddress.value = null;
};

const handleFormSubmit = async (formData) => {
  formLoading.value = true;
  try {
    if (showEditModal.value && editingAddress.value) {
      await addressesService.updateAddress(editingAddress.value.id, formData);
    } else {
      await addressesService.createAddress(formData);
    }
    closeModal();
    await fetchAddresses();
  } catch (err) {
    error.value = err.message || 'Failed to save address';
  } finally {
    formLoading.value = false;
  }
};

const deleteAddress = async (id) => {
  if (!confirm('Are you sure you want to delete this address?')) {
    return;
  }

  actionLoading.value = true;
  try {
    await addressesService.deleteAddress(id);
    await fetchAddresses(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to delete address';
  } finally {
    actionLoading.value = false;
  }
};

const bulkDelete = async () => {
  if (!confirm(`Are you sure you want to delete ${selectedAddresses.value.length} selected addresses?`)) {
    return;
  }

  actionLoading.value = true;
  try {
    // Delete addresses one by one since there's no bulk delete endpoint
    for (const id of selectedAddresses.value) {
      await addressesService.deleteAddress(id);
    }
    await fetchAddresses(); // Refresh data
  } catch (err) {
    error.value = err.message || 'Failed to delete addresses';
  } finally {
    actionLoading.value = false;
  }
};

// Utility methods
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString();
};

// Lifecycle
onMounted(() => {
  fetchAddresses();
});
</script>

<style scoped>
.address-list {
  padding: 1rem;
}

.title {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

.table-container {
  overflow-x: auto;
}

.table {
  background-color: var(--card-bg);
  color: var(--text-primary);
}

.table th {
  background-color: var(--darker-bg);
  color: var(--text-primary);
  border-color: var(--border-color);
}

.table td {
  border-color: var(--border-color);
  color: var(--text-primary);
}

.table tbody tr:hover {
  background-color: var(--darker-bg);
}

.buttons {
  display: flex;
  gap: 0.5rem;
}

.checkbox input[type="checkbox"] {
  background-color: var(--card-bg);
  border-color: var(--border-color);
}

.has-text-grey {
  color: var(--text-secondary) !important;
}

.has-text-link {
  color: var(--accent-color) !important;
}

.button.is-primary {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.button.is-primary:hover {
  background-color: var(--accent-color-dark);
}

.button.is-danger {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.button.is-danger:hover {
  background-color: var(--danger-color-dark);
}

.button.is-info {
  background-color: var(--info-color);
  border-color: var(--info-color);
}

.button.is-info:hover {
  background-color: var(--info-color-dark);
}

.level {
  margin-bottom: 1.5rem;
}

.card {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
}

.notification.is-danger {
  background-color: var(--danger-color);
  color: white;
}

.modal-card {
  background-color: var(--card-bg);
}

.modal-card-head {
  background-color: var(--darker-bg);
  border-color: var(--border-color);
}

.modal-card-title {
  color: var(--text-primary);
}

.modal-card-body {
  background-color: var(--card-bg);
}
</style>
